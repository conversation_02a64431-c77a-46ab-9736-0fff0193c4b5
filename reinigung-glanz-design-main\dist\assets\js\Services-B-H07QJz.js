import{j as e}from"./ui-vendor-WY5xTeZk.js";import"./react-vendor-o6ozJo2K.js";const l=()=>{const s=[{title:"Hotelzimmerreinigung",description:"Tiefenreinigung und tägliche Pflege für höchste Hygienestandards in Hotelzimmern.",icon:"🏨"},{title:"Teppichreinigung",description:"Tiefenreinigung für Teppiche und Polster. Wir entfernen Flecken, Gerüche und Allergene für ein frisches und hygienisches Raumklima.",icon:"🏠"},{title:"Bodenreinigung",description:"Professionelle Pflege für Hartböden, Fliesen, Laminat und mehr. Wir sorgen für glänzende, hygienisch saubere Oberflächen.",icon:"✨"},{title:"Gemeinschaftsräume",description:"Zuverlässige Reinigung von Treppenhäusern, Fluren und Gemeinschaftsbereichen für Mehrfamilienhäuser und Wohnanlagen.",icon:"🏢"},{title:"Büroreinigung",description:"Professionelle Reinigung von Büroflächen und Arbeitsplätzen für ein sauberes und produktives Arbeitsumfeld.",icon:"💼"},{title:"Desinfektion",description:"Gründliche Desinfektion von Räumen und Oberflächen zur Bekämpfung von Keimen, Bakterien und Viren für maximale Hygiene und Sicherheit.",icon:"🧽"}];return e.jsx("section",{id:"services",className:"px-4",style:{padding:"var(--section-padding-lg) var(--space-4)"},children:e.jsxs("div",{className:"max-w-7xl mx-auto",children:[e.jsxs("div",{className:"text-center animate-fade-in",style:{marginBottom:"var(--space-20)"},children:[e.jsxs("h2",{className:"suz-text-display-lg text-slate-800 mb-8",children:["Unsere ",e.jsx("span",{className:"gradient-text",children:"Leistungen"})]}),e.jsx("p",{className:"suz-text-heading-lg text-slate-600 max-w-3xl mx-auto",children:"Professionelle Reinigungslösungen für jeden Bedarf"})]}),e.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-10",children:s.map((n,i)=>e.jsxs("article",{className:"suz-card-glass service-card-premium rounded-3xl border border-white/30 group shadow-2xl animate-fade-in",style:{animationDelay:`${i*.15}s`,padding:"var(--component-padding-lg)"},children:[e.jsx("div",{className:"icon-badge mb-6",role:"img","aria-label":`Icon für ${n.title}`,children:e.jsx("div",{className:"text-5xl group-hover:scale-110 transition-transform duration-300",children:n.icon})}),e.jsx("h3",{className:"suz-text-heading-lg font-semibold text-slate-800 mb-6",children:n.title}),e.jsx("p",{className:"suz-text-body-lg text-slate-600",style:{lineHeight:"var(--line-height-relaxed)"},children:n.description})]},i))})]})})};export{l as default};
