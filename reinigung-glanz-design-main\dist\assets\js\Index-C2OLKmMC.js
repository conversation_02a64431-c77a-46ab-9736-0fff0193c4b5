const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/Hero-Cutbhd5S.js","assets/js/ui-vendor-WY5xTeZk.js","assets/js/react-vendor-o6ozJo2K.js","assets/js/Services-B-H07QJz.js","assets/js/Team-BzhLfW9K.js","assets/js/Contact-ty8W4k3r.js","assets/js/phone-BUrNMtGO.js","assets/js/index-neWIiZQb.js","assets/js/utils-vendor--BulIq_u.js","assets/js/query-vendor--kaXPEoe.js","assets/js/router-vendor-CClBJTgV.js","assets/css/index-Bdvp4ck7.css","assets/js/Footer-BHd9NumO.js"])))=>i.map(i=>d[i]);
import{_ as r}from"./index-neWIiZQb.js";import{j as e}from"./ui-vendor-WY5xTeZk.js";import{r as s}from"./react-vendor-o6ozJo2K.js";import"./utils-vendor--BulIq_u.js";import"./query-vendor--kaXPEoe.js";import"./router-vendor-CClBJTgV.js";const l=({scrollToSection:n})=>e.jsx("nav",{className:"fixed top-6 left-1/2 transform -translate-x-1/2 z-50 animate-fade-in",role:"navigation","aria-label":"Hauptnavigation",children:e.jsx("div",{className:"suz-card-glass px-8 py-4 rounded-full border border-white/30 shadow-xl",children:e.jsxs("div",{className:"flex items-center space-x-4 sm:space-x-8",children:[e.jsx("button",{type:"button",onClick:()=>n("home"),className:"suz-nav-link suz-focus-ring","aria-label":"Zur Startseite navigieren",children:"Startseite"}),e.jsx("button",{type:"button",onClick:()=>n("services"),className:"suz-nav-link suz-focus-ring","aria-label":"Zu unseren Leistungen navigieren",children:"Leistungen"}),e.jsx("button",{type:"button",onClick:()=>n("team"),className:"suz-nav-link suz-focus-ring","aria-label":"Zu unserem Team navigieren",children:"Unser Team"}),e.jsx("button",{type:"button",onClick:()=>n("contact"),className:"suz-nav-link suz-focus-ring","aria-label":"Zum Kontakt navigieren",children:"Kontakt"})]})})}),c=s.lazy(()=>r(()=>import("./Hero-Cutbhd5S.js"),__vite__mapDeps([0,1,2]))),d=s.lazy(()=>r(()=>import("./Services-B-H07QJz.js"),__vite__mapDeps([3,1,2]))),u=s.lazy(()=>r(()=>import("./Team-BzhLfW9K.js"),__vite__mapDeps([4,1,2]))),m=s.lazy(()=>r(()=>import("./Contact-ty8W4k3r.js"),__vite__mapDeps([5,1,2,6,7,8,9,10,11]))),x=s.lazy(()=>r(()=>import("./Footer-BHd9NumO.js"),__vite__mapDeps([12,1,2,6,7,8,9,10,11]))),a=({className:n=""})=>e.jsx("div",{className:`flex items-center justify-center py-16 ${n}`,children:e.jsx("div",{className:"w-8 h-8 border-2 border-blue-200 border-t-blue-600 rounded-full animate-spin"})}),_=()=>{const[n,i]=s.useState(0);s.useEffect(()=>{const t=()=>i(window.scrollY);return window.addEventListener("scroll",t),()=>window.removeEventListener("scroll",t)},[]);const o=t=>{document.getElementById(t)?.scrollIntoView({behavior:"smooth"})};return e.jsxs("div",{className:"min-h-screen bg-premium-gradient overflow-x-hidden force-apple-design",children:[e.jsx(l,{scrollToSection:o}),e.jsx("div",{className:"fixed top-8 left-8 z-50 animate-fade-in",children:e.jsx("div",{className:"glass-morphism-premium p-4 rounded-3xl border border-white/30 shadow-xl backdrop-blur-xl logo-glow group",children:e.jsx("img",{src:"/assets/logos/logo.png",alt:"SUZ Reinigung Logo",className:"w-16 h-16 object-contain transition-all duration-300 group-hover:scale-110 min-w-[40px] min-h-[40px]"})})}),e.jsx(s.Suspense,{fallback:e.jsx(a,{className:"min-h-screen"}),children:e.jsx(c,{scrollY:n})}),e.jsx(s.Suspense,{fallback:e.jsx(a,{}),children:e.jsx(d,{})}),e.jsx(s.Suspense,{fallback:e.jsx(a,{}),children:e.jsx(u,{})}),e.jsx(s.Suspense,{fallback:e.jsx(a,{}),children:e.jsx(m,{})}),e.jsx(s.Suspense,{fallback:e.jsx(a,{}),children:e.jsx(x,{})})]})};export{_ as default};
