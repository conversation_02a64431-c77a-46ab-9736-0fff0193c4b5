
import { Mail, Phone, Users } from 'lucide-react';

const Contact = () => {
  return (
    <section
      id="contact"
      className="px-4"
      style={{ padding: `var(--section-padding-lg) var(--space-4)` }}
    >
      <div className="max-w-5xl mx-auto text-center">
        <div className="suz-card-glass rounded-3xl border border-white/30 animate-fade-in shadow-2xl" style={{ padding: 'var(--space-16)' }}>
          <h2 className="suz-section-title text-slate-800 mb-8">
            <span className="gradient-text">Kontakt</span> aufnehmen
          </h2>
          <p className="suz-text-heading-lg text-slate-600 mb-12">
            Kontaktieren Sie uns jetzt – schnell & unkompliziert!
          </p>

          <div className="flex flex-col sm:flex-row justify-center gap-8 mb-16">
            <a
              href="https://wa.me/*************"
              target="_blank"
              rel="noopener noreferrer"
              className="suz-btn-primary bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 px-12 py-6 rounded-full text-xl shadow-2xl flex items-center justify-center gap-3"
              aria-label="Kontakt über WhatsApp aufnehmen"
            >
              <Phone className="w-6 h-6" aria-hidden="true" />
              WhatsApp
            </a>
            <a
              href="mailto:<EMAIL>"
              className="suz-btn-primary bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 px-12 py-6 rounded-full text-xl shadow-2xl flex items-center justify-center gap-3"
              aria-label="E-Mail an SUZ Reinigung senden"
            >
              <Mail className="w-6 h-6" aria-hidden="true" />
              E-Mail
            </a>
          </div>

          <div className="grid md:grid-cols-3 gap-8 text-slate-600">
            <div className="flex flex-col items-center">
              <Phone className="w-8 h-8 mb-4" style={{ color: 'var(--suz-blue-primary)' }} aria-hidden="true" />
              <h3 className="suz-text-heading-md font-semibold text-slate-800 mb-3">Telefon</h3>
              <p className="suz-text-body-lg">
                <a href="tel:+*************" className="hover:text-blue-600 transition-colors">
                  +49 176 23152477
                </a>
              </p>
            </div>
            <div className="flex flex-col items-center">
              <Mail className="w-8 h-8 mb-4" style={{ color: 'var(--suz-blue-primary)' }} aria-hidden="true" />
              <h3 className="suz-text-heading-md font-semibold text-slate-800 mb-3">E-Mail</h3>
              <p className="suz-text-body-lg">
                <a href="mailto:<EMAIL>" className="hover:text-blue-600 transition-colors">
                  <EMAIL>
                </a>
              </p>
            </div>
            <div className="flex flex-col items-center">
              <Users className="w-8 h-8 mb-4" style={{ color: 'var(--suz-blue-primary)' }} aria-hidden="true" />
              <h3 className="suz-text-heading-md font-semibold text-slate-800 mb-3">Adresse</h3>
              <address className="suz-text-body-lg not-italic">
                Paul-Langen-Straße 39<br />53229 Bonn
              </address>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
