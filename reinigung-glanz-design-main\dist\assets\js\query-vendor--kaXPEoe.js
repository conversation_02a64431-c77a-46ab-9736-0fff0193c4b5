import{r as G}from"./react-vendor-o6ozJo2K.js";import{j as J}from"./ui-vendor-WY5xTeZk.js";var q=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},E=typeof window>"u"||"Deno"in globalThis;function p(){}function X(t,e){return typeof t=="function"?t(e):t}function Y(t){return typeof t=="number"&&t>=0&&t!==1/0}function Z(t,e){return Math.max(t+(e||0)-Date.now(),0)}function A(t,e){return typeof t=="function"?t(e):t}function tt(t,e){return typeof t=="function"?t(e):t}function U(t,e){const{type:s="all",exact:i,fetchStatus:r,predicate:a,queryKey:u,stale:o}=t;if(u){if(i){if(e.queryHash!==j(u,e.options))return!1}else if(!F(e.queryKey,u))return!1}if(s!=="all"){const n=e.isActive();if(s==="active"&&!n||s==="inactive"&&n)return!1}return!(typeof o=="boolean"&&e.isStale()!==o||r&&r!==e.state.fetchStatus||a&&!a(e))}function I(t,e){const{exact:s,status:i,predicate:r,mutationKey:a}=t;if(a){if(!e.options.mutationKey)return!1;if(s){if(P(e.options.mutationKey)!==P(a))return!1}else if(!F(e.options.mutationKey,a))return!1}return!(i&&e.state.status!==i||r&&!r(e))}function j(t,e){return(e?.queryKeyHashFn||P)(t)}function P(t){return JSON.stringify(t,(e,s)=>R(s)?Object.keys(s).sort().reduce((i,r)=>(i[r]=s[r],i),{}):s)}function F(t,e){return t===e?!0:typeof t!=typeof e?!1:t&&e&&typeof t=="object"&&typeof e=="object"?Object.keys(e).every(s=>F(t[s],e[s])):!1}function N(t,e){if(t===e)return t;const s=K(t)&&K(e);if(s||R(t)&&R(e)){const i=s?t:Object.keys(t),r=i.length,a=s?e:Object.keys(e),u=a.length,o=s?[]:{},n=new Set(i);let l=0;for(let y=0;y<u;y++){const c=s?y:a[y];(!s&&n.has(c)||s)&&t[c]===void 0&&e[c]===void 0?(o[c]=void 0,l++):(o[c]=N(t[c],e[c]),o[c]===t[c]&&t[c]!==void 0&&l++)}return r===u&&l===r?t:o}return e}function K(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function R(t){if(!k(t))return!1;const e=t.constructor;if(e===void 0)return!0;const s=e.prototype;return!(!k(s)||!s.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(t)!==Object.prototype)}function k(t){return Object.prototype.toString.call(t)==="[object Object]"}function et(t){return new Promise(e=>{setTimeout(e,t)})}function st(t,e,s){return typeof s.structuralSharing=="function"?s.structuralSharing(t,e):s.structuralSharing!==!1?N(t,e):e}function it(t,e,s=0){const i=[...t,e];return s&&i.length>s?i.slice(1):i}function rt(t,e,s=0){const i=[e,...t];return s&&i.length>s?i.slice(0,-1):i}var x=Symbol();function B(t,e){return!t.queryFn&&e?.initialPromise?()=>e.initialPromise:!t.queryFn||t.queryFn===x?()=>Promise.reject(new Error(`Missing queryFn: '${t.queryHash}'`)):t.queryFn}var nt=class extends q{#t;#e;#s;constructor(){super(),this.#s=t=>{if(!E&&window.addEventListener){const e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}}}onSubscribe(){this.#e||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#s=t,this.#e?.(),this.#e=t(e=>{typeof e=="boolean"?this.setFocused(e):this.onFocus()})}setFocused(t){this.#t!==t&&(this.#t=t,this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(e=>{e(t)})}isFocused(){return typeof this.#t=="boolean"?this.#t:globalThis.document?.visibilityState!=="hidden"}},_=new nt,at=class extends q{#t=!0;#e;#s;constructor(){super(),this.#s=t=>{if(!E&&window.addEventListener){const e=()=>t(!0),s=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",s)}}}}onSubscribe(){this.#e||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#s=t,this.#e?.(),this.#e=t(this.setOnline.bind(this))}setOnline(t){this.#t!==t&&(this.#t=t,this.listeners.forEach(s=>{s(t)}))}isOnline(){return this.#t}},Q=new at;function ot(){let t,e;const s=new Promise((r,a)=>{t=r,e=a});s.status="pending",s.catch(()=>{});function i(r){Object.assign(s,r),delete s.resolve,delete s.reject}return s.resolve=r=>{i({status:"fulfilled",value:r}),t(r)},s.reject=r=>{i({status:"rejected",reason:r}),e(r)},s}function ut(t){return Math.min(1e3*2**t,3e4)}function z(t){return(t??"online")==="online"?Q.isOnline():!0}var W=class extends Error{constructor(t){super("CancelledError"),this.revert=t?.revert,this.silent=t?.silent}};function D(t){return t instanceof W}function $(t){let e=!1,s=0,i=!1,r;const a=ot(),u=h=>{i||(b(new W(h)),t.abort?.())},o=()=>{e=!0},n=()=>{e=!1},l=()=>_.isFocused()&&(t.networkMode==="always"||Q.isOnline())&&t.canRun(),y=()=>z(t.networkMode)&&t.canRun(),c=h=>{i||(i=!0,t.onSuccess?.(h),r?.(),a.resolve(h))},b=h=>{i||(i=!0,t.onError?.(h),r?.(),a.reject(h))},w=()=>new Promise(h=>{r=v=>{(i||l())&&h(v)},t.onPause?.()}).then(()=>{r=void 0,i||t.onContinue?.()}),f=()=>{if(i)return;let h;const v=s===0?t.initialPromise:void 0;try{h=v??t.fn()}catch(m){h=Promise.reject(m)}Promise.resolve(h).then(c).catch(m=>{if(i)return;const g=t.retry??(E?0:3),O=t.retryDelay??ut,S=typeof O=="function"?O(s,m):O,C=g===!0||typeof g=="number"&&s<g||typeof g=="function"&&g(s,m);if(e||!C){b(m);return}s++,t.onFail?.(s,m),et(S).then(()=>l()?void 0:w()).then(()=>{e?b(m):f()})})};return{promise:a,cancel:u,continue:()=>(r?.(),a),cancelRetry:o,continueRetry:n,canStart:y,start:()=>(y()?f():w().then(f),a)}}var ht=t=>setTimeout(t,0);function ct(){let t=[],e=0,s=o=>{o()},i=o=>{o()},r=ht;const a=o=>{e?t.push(o):r(()=>{s(o)})},u=()=>{const o=t;t=[],o.length&&r(()=>{i(()=>{o.forEach(n=>{s(n)})})})};return{batch:o=>{let n;e++;try{n=o()}finally{e--,e||u()}return n},batchCalls:o=>(...n)=>{a(()=>{o(...n)})},schedule:a,setNotifyFunction:o=>{s=o},setBatchNotifyFunction:o=>{i=o},setScheduler:o=>{r=o}}}var d=ct(),V=class{#t;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),Y(this.gcTime)&&(this.#t=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(E?1/0:5*60*1e3))}clearGcTimeout(){this.#t&&(clearTimeout(this.#t),this.#t=void 0)}},lt=class extends V{#t;#e;#s;#r;#i;#a;#o;constructor(t){super(),this.#o=!1,this.#a=t.defaultOptions,this.setOptions(t.options),this.observers=[],this.#r=t.client,this.#s=this.#r.getQueryCache(),this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.#t=dt(this.options),this.state=t.state??this.#t,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#i?.promise}setOptions(t){this.options={...this.#a,...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&this.#s.remove(this)}setData(t,e){const s=st(this.state.data,t,this.options);return this.#n({data:s,type:"success",dataUpdatedAt:e?.updatedAt,manual:e?.manual}),s}setState(t,e){this.#n({type:"setState",state:t,setStateOptions:e})}cancel(t){const e=this.#i?.promise;return this.#i?.cancel(t),e?e.then(p).catch(p):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#t)}isActive(){return this.observers.some(t=>tt(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===x||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(t=>A(t.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(t=0){return this.state.data===void 0?!0:t==="static"?!1:this.state.isInvalidated?!0:!Z(this.state.dataUpdatedAt,t)}onFocus(){this.observers.find(e=>e.shouldFetchOnWindowFocus())?.refetch({cancelRefetch:!1}),this.#i?.continue()}onOnline(){this.observers.find(e=>e.shouldFetchOnReconnect())?.refetch({cancelRefetch:!1}),this.#i?.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),this.#s.notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(e=>e!==t),this.observers.length||(this.#i&&(this.#o?this.#i.cancel({revert:!0}):this.#i.cancelRetry()),this.scheduleGc()),this.#s.notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#n({type:"invalidate"})}fetch(t,e){if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&e?.cancelRefetch)this.cancel({silent:!0});else if(this.#i)return this.#i.continueRetry(),this.#i.promise}if(t&&this.setOptions(t),!this.options.queryFn){const n=this.observers.find(l=>l.options.queryFn);n&&this.setOptions(n.options)}const s=new AbortController,i=n=>{Object.defineProperty(n,"signal",{enumerable:!0,get:()=>(this.#o=!0,s.signal)})},r=()=>{const n=B(this.options,e),y=(()=>{const c={client:this.#r,queryKey:this.queryKey,meta:this.meta};return i(c),c})();return this.#o=!1,this.options.persister?this.options.persister(n,y,this):n(y)},u=(()=>{const n={fetchOptions:e,options:this.options,queryKey:this.queryKey,client:this.#r,state:this.state,fetchFn:r};return i(n),n})();this.options.behavior?.onFetch(u,this),this.#e=this.state,(this.state.fetchStatus==="idle"||this.state.fetchMeta!==u.fetchOptions?.meta)&&this.#n({type:"fetch",meta:u.fetchOptions?.meta});const o=n=>{D(n)&&n.silent||this.#n({type:"error",error:n}),D(n)||(this.#s.config.onError?.(n,this),this.#s.config.onSettled?.(this.state.data,n,this)),this.scheduleGc()};return this.#i=$({initialPromise:e?.initialPromise,fn:u.fetchFn,abort:s.abort.bind(s),onSuccess:n=>{if(n===void 0){o(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(n)}catch(l){o(l);return}this.#s.config.onSuccess?.(n,this),this.#s.config.onSettled?.(n,this.state.error,this),this.scheduleGc()},onError:o,onFail:(n,l)=>{this.#n({type:"failed",failureCount:n,error:l})},onPause:()=>{this.#n({type:"pause"})},onContinue:()=>{this.#n({type:"continue"})},retry:u.options.retry,retryDelay:u.options.retryDelay,networkMode:u.options.networkMode,canRun:()=>!0}),this.#i.start()}#n(t){const e=s=>{switch(t.type){case"failed":return{...s,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...s,fetchStatus:"paused"};case"continue":return{...s,fetchStatus:"fetching"};case"fetch":return{...s,...ft(s.data,this.options),fetchMeta:t.meta??null};case"success":return this.#e=void 0,{...s,data:t.data,dataUpdateCount:s.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const i=t.error;return D(i)&&i.revert&&this.#e?{...this.#e,fetchStatus:"idle"}:{...s,error:i,errorUpdateCount:s.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:s.fetchFailureCount+1,fetchFailureReason:i,fetchStatus:"idle",status:"error"};case"invalidate":return{...s,isInvalidated:!0};case"setState":return{...s,...t.state}}};this.state=e(this.state),d.batch(()=>{this.observers.forEach(s=>{s.onQueryUpdate()}),this.#s.notify({query:this,type:"updated",action:t})})}};function ft(t,e){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:z(e.networkMode)?"fetching":"paused",...t===void 0&&{error:null,status:"pending"}}}function dt(t){const e=typeof t.initialData=="function"?t.initialData():t.initialData,s=e!==void 0,i=s?typeof t.initialDataUpdatedAt=="function"?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:s?i??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}var yt=class extends q{constructor(t={}){super(),this.config=t,this.#t=new Map}#t;build(t,e,s){const i=e.queryKey,r=e.queryHash??j(i,e);let a=this.get(r);return a||(a=new lt({client:t,queryKey:i,queryHash:r,options:t.defaultQueryOptions(e),state:s,defaultOptions:t.getQueryDefaults(i)}),this.add(a)),a}add(t){this.#t.has(t.queryHash)||(this.#t.set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const e=this.#t.get(t.queryHash);e&&(t.destroy(),e===t&&this.#t.delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){d.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return this.#t.get(t)}getAll(){return[...this.#t.values()]}find(t){const e={exact:!0,...t};return this.getAll().find(s=>U(e,s))}findAll(t={}){const e=this.getAll();return Object.keys(t).length>0?e.filter(s=>U(t,s)):e}notify(t){d.batch(()=>{this.listeners.forEach(e=>{e(t)})})}onFocus(){d.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){d.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},pt=class extends V{#t;#e;#s;constructor(t){super(),this.mutationId=t.mutationId,this.#e=t.mutationCache,this.#t=[],this.state=t.state||mt(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){this.#t.includes(t)||(this.#t.push(t),this.clearGcTimeout(),this.#e.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.#t=this.#t.filter(e=>e!==t),this.scheduleGc(),this.#e.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.#t.length||(this.state.status==="pending"?this.scheduleGc():this.#e.remove(this))}continue(){return this.#s?.continue()??this.execute(this.state.variables)}async execute(t){const e=()=>{this.#r({type:"continue"})};this.#s=$({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(r,a)=>{this.#r({type:"failed",failureCount:r,error:a})},onPause:()=>{this.#r({type:"pause"})},onContinue:e,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#e.canRun(this)});const s=this.state.status==="pending",i=!this.#s.canStart();try{if(s)e();else{this.#r({type:"pending",variables:t,isPaused:i}),await this.#e.config.onMutate?.(t,this);const a=await this.options.onMutate?.(t);a!==this.state.context&&this.#r({type:"pending",context:a,variables:t,isPaused:i})}const r=await this.#s.start();return await this.#e.config.onSuccess?.(r,t,this.state.context,this),await this.options.onSuccess?.(r,t,this.state.context),await this.#e.config.onSettled?.(r,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(r,null,t,this.state.context),this.#r({type:"success",data:r}),r}catch(r){try{throw await this.#e.config.onError?.(r,t,this.state.context,this),await this.options.onError?.(r,t,this.state.context),await this.#e.config.onSettled?.(void 0,r,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,r,t,this.state.context),r}finally{this.#r({type:"error",error:r})}}finally{this.#e.runNext(this)}}#r(t){const e=s=>{switch(t.type){case"failed":return{...s,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...s,isPaused:!0};case"continue":return{...s,isPaused:!1};case"pending":return{...s,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...s,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...s,data:void 0,error:t.error,failureCount:s.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=e(this.state),d.batch(()=>{this.#t.forEach(s=>{s.onMutationUpdate(t)}),this.#e.notify({mutation:this,type:"updated",action:t})})}};function mt(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var vt=class extends q{constructor(t={}){super(),this.config=t,this.#t=new Set,this.#e=new Map,this.#s=0}#t;#e;#s;build(t,e,s){const i=new pt({mutationCache:this,mutationId:++this.#s,options:t.defaultMutationOptions(e),state:s});return this.add(i),i}add(t){this.#t.add(t);const e=M(t);if(typeof e=="string"){const s=this.#e.get(e);s?s.push(t):this.#e.set(e,[t])}this.notify({type:"added",mutation:t})}remove(t){if(this.#t.delete(t)){const e=M(t);if(typeof e=="string"){const s=this.#e.get(e);if(s)if(s.length>1){const i=s.indexOf(t);i!==-1&&s.splice(i,1)}else s[0]===t&&this.#e.delete(e)}}this.notify({type:"removed",mutation:t})}canRun(t){const e=M(t);if(typeof e=="string"){const i=this.#e.get(e)?.find(r=>r.state.status==="pending");return!i||i===t}else return!0}runNext(t){const e=M(t);return typeof e=="string"?this.#e.get(e)?.find(i=>i!==t&&i.state.isPaused)?.continue()??Promise.resolve():Promise.resolve()}clear(){d.batch(()=>{this.#t.forEach(t=>{this.notify({type:"removed",mutation:t})}),this.#t.clear(),this.#e.clear()})}getAll(){return Array.from(this.#t)}find(t){const e={exact:!0,...t};return this.getAll().find(s=>I(e,s))}findAll(t={}){return this.getAll().filter(e=>I(t,e))}notify(t){d.batch(()=>{this.listeners.forEach(e=>{e(t)})})}resumePausedMutations(){const t=this.getAll().filter(e=>e.state.isPaused);return d.batch(()=>Promise.all(t.map(e=>e.continue().catch(p))))}};function M(t){return t.options.scope?.id}function L(t){return{onFetch:(e,s)=>{const i=e.options,r=e.fetchOptions?.meta?.fetchMore?.direction,a=e.state.data?.pages||[],u=e.state.data?.pageParams||[];let o={pages:[],pageParams:[]},n=0;const l=async()=>{let y=!1;const c=f=>{Object.defineProperty(f,"signal",{enumerable:!0,get:()=>(e.signal.aborted?y=!0:e.signal.addEventListener("abort",()=>{y=!0}),e.signal)})},b=B(e.options,e.fetchOptions),w=async(f,h,v)=>{if(y)return Promise.reject();if(h==null&&f.pages.length)return Promise.resolve(f);const g=(()=>{const T={client:e.client,queryKey:e.queryKey,pageParam:h,direction:v?"backward":"forward",meta:e.options.meta};return c(T),T})(),O=await b(g),{maxPages:S}=e.options,C=v?rt:it;return{pages:C(f.pages,O,S),pageParams:C(f.pageParams,h,S)}};if(r&&a.length){const f=r==="backward",h=f?gt:H,v={pages:a,pageParams:u},m=h(i,v);o=await w(v,m,f)}else{const f=t??a.length;do{const h=n===0?u[0]??i.initialPageParam:H(i,o);if(n>0&&h==null)break;o=await w(o,h),n++}while(n<f)}return o};e.options.persister?e.fetchFn=()=>e.options.persister?.(l,{client:e.client,queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},s):e.fetchFn=l}}}function H(t,{pages:e,pageParams:s}){const i=e.length-1;return e.length>0?t.getNextPageParam(e[i],e,s[i],s):void 0}function gt(t,{pages:e,pageParams:s}){return e.length>0?t.getPreviousPageParam?.(e[0],e,s[0],s):void 0}var Pt=class{#t;#e;#s;#r;#i;#a;#o;#n;constructor(t={}){this.#t=t.queryCache||new yt,this.#e=t.mutationCache||new vt,this.#s=t.defaultOptions||{},this.#r=new Map,this.#i=new Map,this.#a=0}mount(){this.#a++,this.#a===1&&(this.#o=_.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#t.onFocus())}),this.#n=Q.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#t.onOnline())}))}unmount(){this.#a--,this.#a===0&&(this.#o?.(),this.#o=void 0,this.#n?.(),this.#n=void 0)}isFetching(t){return this.#t.findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return this.#e.findAll({...t,status:"pending"}).length}getQueryData(t){const e=this.defaultQueryOptions({queryKey:t});return this.#t.get(e.queryHash)?.state.data}ensureQueryData(t){const e=this.defaultQueryOptions(t),s=this.#t.build(this,e),i=s.state.data;return i===void 0?this.fetchQuery(t):(t.revalidateIfStale&&s.isStaleByTime(A(e.staleTime,s))&&this.prefetchQuery(e),Promise.resolve(i))}getQueriesData(t){return this.#t.findAll(t).map(({queryKey:e,state:s})=>{const i=s.data;return[e,i]})}setQueryData(t,e,s){const i=this.defaultQueryOptions({queryKey:t}),a=this.#t.get(i.queryHash)?.state.data,u=X(e,a);if(u!==void 0)return this.#t.build(this,i).setData(u,{...s,manual:!0})}setQueriesData(t,e,s){return d.batch(()=>this.#t.findAll(t).map(({queryKey:i})=>[i,this.setQueryData(i,e,s)]))}getQueryState(t){const e=this.defaultQueryOptions({queryKey:t});return this.#t.get(e.queryHash)?.state}removeQueries(t){const e=this.#t;d.batch(()=>{e.findAll(t).forEach(s=>{e.remove(s)})})}resetQueries(t,e){const s=this.#t;return d.batch(()=>(s.findAll(t).forEach(i=>{i.reset()}),this.refetchQueries({type:"active",...t},e)))}cancelQueries(t,e={}){const s={revert:!0,...e},i=d.batch(()=>this.#t.findAll(t).map(r=>r.cancel(s)));return Promise.all(i).then(p).catch(p)}invalidateQueries(t,e={}){return d.batch(()=>(this.#t.findAll(t).forEach(s=>{s.invalidate()}),t?.refetchType==="none"?Promise.resolve():this.refetchQueries({...t,type:t?.refetchType??t?.type??"active"},e)))}refetchQueries(t,e={}){const s={...e,cancelRefetch:e.cancelRefetch??!0},i=d.batch(()=>this.#t.findAll(t).filter(r=>!r.isDisabled()&&!r.isStatic()).map(r=>{let a=r.fetch(void 0,s);return s.throwOnError||(a=a.catch(p)),r.state.fetchStatus==="paused"?Promise.resolve():a}));return Promise.all(i).then(p)}fetchQuery(t){const e=this.defaultQueryOptions(t);e.retry===void 0&&(e.retry=!1);const s=this.#t.build(this,e);return s.isStaleByTime(A(e.staleTime,s))?s.fetch(e):Promise.resolve(s.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(p).catch(p)}fetchInfiniteQuery(t){return t.behavior=L(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(p).catch(p)}ensureInfiniteQueryData(t){return t.behavior=L(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return Q.isOnline()?this.#e.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#t}getMutationCache(){return this.#e}getDefaultOptions(){return this.#s}setDefaultOptions(t){this.#s=t}setQueryDefaults(t,e){this.#r.set(P(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){const e=[...this.#r.values()],s={};return e.forEach(i=>{F(t,i.queryKey)&&Object.assign(s,i.defaultOptions)}),s}setMutationDefaults(t,e){this.#i.set(P(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){const e=[...this.#i.values()],s={};return e.forEach(i=>{F(t,i.mutationKey)&&Object.assign(s,i.defaultOptions)}),s}defaultQueryOptions(t){if(t._defaulted)return t;const e={...this.#s.queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=j(e.queryKey,e)),e.refetchOnReconnect===void 0&&(e.refetchOnReconnect=e.networkMode!=="always"),e.throwOnError===void 0&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),e.queryFn===x&&(e.enabled=!1),e}defaultMutationOptions(t){return t?._defaulted?t:{...this.#s.mutations,...t?.mutationKey&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){this.#t.clear(),this.#e.clear()}},bt=G.createContext(void 0),Ft=({client:t,children:e})=>(G.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]),J.jsx(bt.Provider,{value:t,children:e}));export{Pt as Q,Ft as a};
