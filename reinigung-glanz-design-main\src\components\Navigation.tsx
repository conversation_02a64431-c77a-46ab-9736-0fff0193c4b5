
interface NavigationProps {
  scrollToSection: (id: string) => void;
}

const Navigation = ({ scrollToSection }: NavigationProps) => {
  return (
    <nav
      className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 animate-fade-in"
      role="navigation"
      aria-label="Hauptnavigation"
    >
      <div className="suz-card-glass px-8 py-4 rounded-full border border-white/30 shadow-xl">
        <div className="flex items-center justify-center space-x-4 sm:space-x-8">
          <button
            type="button"
            onClick={() => scrollToSection('home')}
            className="suz-nav-link suz-focus-ring"
            aria-label="Zur Startseite navigieren"
          >
            Startseite
          </button>
          <button
            type="button"
            onClick={() => scrollToSection('services')}
            className="suz-nav-link suz-focus-ring"
            aria-label="Zu unseren Leistungen navigieren"
          >
            Leistungen
          </button>
          <button
            type="button"
            onClick={() => scrollToSection('team')}
            className="suz-nav-link suz-focus-ring"
            aria-label="Zu unserem Team navigieren"
          >
            Unser Team
          </button>
          <button
            type="button"
            onClick={() => scrollToSection('contact')}
            className="suz-nav-link suz-focus-ring"
            aria-label="Zum Kontakt navigieren"
          >
            Kontakt
          </button>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
