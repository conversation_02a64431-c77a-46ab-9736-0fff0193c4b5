import{r as u,o as ne,a as Xe,v as Rn,R as An}from"./react-vendor-o6ozJo2K.js";var Tt={exports:{}},Ae={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sn=u,On=Symbol.for("react.element"),Dn=Symbol.for("react.fragment"),Nn=Object.prototype.hasOwnProperty,Ln=Sn.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,_n={key:!0,ref:!0,__self:!0,__source:!0};function Ct(e,t,n){var o,r={},s=null,i=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(i=t.ref);for(o in t)Nn.call(t,o)&&!_n.hasOwnProperty(o)&&(r[o]=t[o]);if(e&&e.defaultProps)for(o in t=e.defaultProps,t)r[o]===void 0&&(r[o]=t[o]);return{$$typeof:On,type:e,key:s,ref:i,props:r,_owner:Ln.current}}Ae.Fragment=Dn;Ae.jsx=Ct;Ae.jsxs=Ct;Tt.exports=Ae;var b=Tt.exports;function j(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),n===!1||!r.defaultPrevented)return t?.(r)}}function ut(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function Pt(...e){return t=>{let n=!1;const o=e.map(r=>{const s=ut(r,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let r=0;r<o.length;r++){const s=o[r];typeof s=="function"?s():ut(e[r],null)}}}}function V(...e){return u.useCallback(Pt(...e),e)}function Se(e,t=[]){let n=[];function o(s,i){const a=u.createContext(i),l=n.length;n=[...n,i];const c=d=>{const{scope:v,children:p,...g}=d,h=v?.[e]?.[l]||a,m=u.useMemo(()=>g,Object.values(g));return b.jsx(h.Provider,{value:m,children:p})};c.displayName=s+"Provider";function f(d,v){const p=v?.[e]?.[l]||a,g=u.useContext(p);if(g)return g;if(i!==void 0)return i;throw new Error(`\`${d}\` must be used within \`${s}\``)}return[c,f]}const r=()=>{const s=n.map(i=>u.createContext(i));return function(a){const l=a?.[e]||s;return u.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return r.scopeName=e,[o,Mn(r,...t)]}function Mn(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const o=e.map(r=>({useScope:r(),scopeName:r.scopeName}));return function(s){const i=o.reduce((a,{useScope:l,scopeName:c})=>{const d=l(s)[`__scope${c}`];return{...a,...d}},{});return u.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}function $e(e){const t=In(e),n=u.forwardRef((o,r)=>{const{children:s,...i}=o,a=u.Children.toArray(s),l=a.find(kn);if(l){const c=l.props.children,f=a.map(d=>d===l?u.Children.count(c)>1?u.Children.only(null):u.isValidElement(c)?c.props.children:null:d);return b.jsx(t,{...i,ref:r,children:u.isValidElement(c)?u.cloneElement(c,void 0,f):null})}return b.jsx(t,{...i,ref:r,children:s})});return n.displayName=`${e}.Slot`,n}function In(e){const t=u.forwardRef((n,o)=>{const{children:r,...s}=n;if(u.isValidElement(r)){const i=$n(r),a=jn(s,r.props);return r.type!==u.Fragment&&(a.ref=o?Pt(o,i):i),u.cloneElement(r,a)}return u.Children.count(r)>1?u.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var Rt=Symbol("radix.slottable");function Fn(e){const t=({children:n})=>b.jsx(b.Fragment,{children:n});return t.displayName=`${e}.Slottable`,t.__radixId=Rt,t}function kn(e){return u.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===Rt}function jn(e,t){const n={...t};for(const o in t){const r=e[o],s=t[o];/^on[A-Z]/.test(o)?r&&s?n[o]=(...a)=>{const l=s(...a);return r(...a),l}:r&&(n[o]=r):o==="style"?n[o]={...r,...s}:o==="className"&&(n[o]=[r,s].filter(Boolean).join(" "))}return{...e,...n}}function $n(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Wn(e){const t=e+"CollectionProvider",[n,o]=Se(t),[r,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=h=>{const{scope:m,children:y}=h,w=ne.useRef(null),x=ne.useRef(new Map).current;return b.jsx(r,{scope:m,itemMap:x,collectionRef:w,children:y})};i.displayName=t;const a=e+"CollectionSlot",l=$e(a),c=ne.forwardRef((h,m)=>{const{scope:y,children:w}=h,x=s(a,y),E=V(m,x.collectionRef);return b.jsx(l,{ref:E,children:w})});c.displayName=a;const f=e+"CollectionItemSlot",d="data-radix-collection-item",v=$e(f),p=ne.forwardRef((h,m)=>{const{scope:y,children:w,...x}=h,E=ne.useRef(null),T=V(m,E),R=s(f,y);return ne.useEffect(()=>(R.itemMap.set(E,{ref:E,...x}),()=>void R.itemMap.delete(E))),b.jsx(v,{[d]:"",ref:T,children:w})});p.displayName=f;function g(h){const m=s(e+"CollectionConsumer",h);return ne.useCallback(()=>{const w=m.collectionRef.current;if(!w)return[];const x=Array.from(w.querySelectorAll(`[${d}]`));return Array.from(m.itemMap.values()).sort((R,P)=>x.indexOf(R.ref.current)-x.indexOf(P.ref.current))},[m.collectionRef,m.itemMap])}return[{Provider:i,Slot:c,ItemSlot:p},g,o]}var Hn=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],$=Hn.reduce((e,t)=>{const n=$e(`Primitive.${t}`),o=u.forwardRef((r,s)=>{const{asChild:i,...a}=r,l=i?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),b.jsx(l,{...a,ref:s})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function At(e,t){e&&Xe.flushSync(()=>e.dispatchEvent(t))}function J(e){const t=u.useRef(e);return u.useEffect(()=>{t.current=e}),u.useMemo(()=>(...n)=>t.current?.(...n),[])}function Bn(e,t=globalThis?.document){const n=J(e);u.useEffect(()=>{const o=r=>{r.key==="Escape"&&n(r)};return t.addEventListener("keydown",o,{capture:!0}),()=>t.removeEventListener("keydown",o,{capture:!0})},[n,t])}var Vn="DismissableLayer",We="dismissableLayer.update",zn="dismissableLayer.pointerDownOutside",Un="dismissableLayer.focusOutside",ft,St=u.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),qe=u.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:o,onPointerDownOutside:r,onFocusOutside:s,onInteractOutside:i,onDismiss:a,...l}=e,c=u.useContext(St),[f,d]=u.useState(null),v=f?.ownerDocument??globalThis?.document,[,p]=u.useState({}),g=V(t,P=>d(P)),h=Array.from(c.layers),[m]=[...c.layersWithOutsidePointerEventsDisabled].slice(-1),y=h.indexOf(m),w=f?h.indexOf(f):-1,x=c.layersWithOutsidePointerEventsDisabled.size>0,E=w>=y,T=Yn(P=>{const O=P.target,L=[...c.branches].some(D=>D.contains(O));!E||L||(r?.(P),i?.(P),P.defaultPrevented||a?.())},v),R=Xn(P=>{const O=P.target;[...c.branches].some(D=>D.contains(O))||(s?.(P),i?.(P),P.defaultPrevented||a?.())},v);return Bn(P=>{w===c.layers.size-1&&(o?.(P),!P.defaultPrevented&&a&&(P.preventDefault(),a()))},v),u.useEffect(()=>{if(f)return n&&(c.layersWithOutsidePointerEventsDisabled.size===0&&(ft=v.body.style.pointerEvents,v.body.style.pointerEvents="none"),c.layersWithOutsidePointerEventsDisabled.add(f)),c.layers.add(f),dt(),()=>{n&&c.layersWithOutsidePointerEventsDisabled.size===1&&(v.body.style.pointerEvents=ft)}},[f,v,n,c]),u.useEffect(()=>()=>{f&&(c.layers.delete(f),c.layersWithOutsidePointerEventsDisabled.delete(f),dt())},[f,c]),u.useEffect(()=>{const P=()=>p({});return document.addEventListener(We,P),()=>document.removeEventListener(We,P)},[]),b.jsx($.div,{...l,ref:g,style:{pointerEvents:x?E?"auto":"none":void 0,...e.style},onFocusCapture:j(e.onFocusCapture,R.onFocusCapture),onBlurCapture:j(e.onBlurCapture,R.onBlurCapture),onPointerDownCapture:j(e.onPointerDownCapture,T.onPointerDownCapture)})});qe.displayName=Vn;var Kn="DismissableLayerBranch",Ot=u.forwardRef((e,t)=>{const n=u.useContext(St),o=u.useRef(null),r=V(t,o);return u.useEffect(()=>{const s=o.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),b.jsx($.div,{...e,ref:r})});Ot.displayName=Kn;function Yn(e,t=globalThis?.document){const n=J(e),o=u.useRef(!1),r=u.useRef(()=>{});return u.useEffect(()=>{const s=a=>{if(a.target&&!o.current){let l=function(){Dt(zn,n,c,{discrete:!0})};const c={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",r.current),r.current=l,t.addEventListener("click",r.current,{once:!0})):l()}else t.removeEventListener("click",r.current);o.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",s),t.removeEventListener("click",r.current)}},[t,n]),{onPointerDownCapture:()=>o.current=!0}}function Xn(e,t=globalThis?.document){const n=J(e),o=u.useRef(!1);return u.useEffect(()=>{const r=s=>{s.target&&!o.current&&Dt(Un,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",r),()=>t.removeEventListener("focusin",r)},[t,n]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}function dt(){const e=new CustomEvent(We);document.dispatchEvent(e)}function Dt(e,t,n,{discrete:o}){const r=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&r.addEventListener(e,t,{once:!0}),o?At(r,s):r.dispatchEvent(s)}var qn=qe,Gn=Ot,Q=globalThis?.document?u.useLayoutEffect:()=>{},Zn="Portal",Nt=u.forwardRef((e,t)=>{const{container:n,...o}=e,[r,s]=u.useState(!1);Q(()=>s(!0),[]);const i=n||r&&globalThis?.document?.body;return i?Rn.createPortal(b.jsx($.div,{...o,ref:t}),i):null});Nt.displayName=Zn;function Jn(e,t){return u.useReducer((n,o)=>t[n][o]??n,e)}var Ge=e=>{const{present:t,children:n}=e,o=Qn(t),r=typeof n=="function"?n({present:o.isPresent}):u.Children.only(n),s=V(o.ref,eo(r));return typeof n=="function"||o.isPresent?u.cloneElement(r,{ref:s}):null};Ge.displayName="Presence";function Qn(e){const[t,n]=u.useState(),o=u.useRef(null),r=u.useRef(e),s=u.useRef("none"),i=e?"mounted":"unmounted",[a,l]=Jn(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return u.useEffect(()=>{const c=ye(o.current);s.current=a==="mounted"?c:"none"},[a]),Q(()=>{const c=o.current,f=r.current;if(f!==e){const v=s.current,p=ye(c);e?l("MOUNT"):p==="none"||c?.display==="none"?l("UNMOUNT"):l(f&&v!==p?"ANIMATION_OUT":"UNMOUNT"),r.current=e}},[e,l]),Q(()=>{if(t){let c;const f=t.ownerDocument.defaultView??window,d=p=>{const h=ye(o.current).includes(p.animationName);if(p.target===t&&h&&(l("ANIMATION_END"),!r.current)){const m=t.style.animationFillMode;t.style.animationFillMode="forwards",c=f.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=m)})}},v=p=>{p.target===t&&(s.current=ye(o.current))};return t.addEventListener("animationstart",v),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{f.clearTimeout(c),t.removeEventListener("animationstart",v),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:u.useCallback(c=>{o.current=c?getComputedStyle(c):null,n(c)},[])}}function ye(e){return e?.animationName||"none"}function eo(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var to=An[" useInsertionEffect ".trim().toString()]||Q;function no({prop:e,defaultProp:t,onChange:n=()=>{},caller:o}){const[r,s,i]=oo({defaultProp:t,onChange:n}),a=e!==void 0,l=a?e:r;{const f=u.useRef(e!==void 0);u.useEffect(()=>{const d=f.current;d!==a&&console.warn(`${o} is changing from ${d?"controlled":"uncontrolled"} to ${a?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),f.current=a},[a,o])}const c=u.useCallback(f=>{if(a){const d=ro(f)?f(e):f;d!==e&&i.current?.(d)}else s(f)},[a,e,s,i]);return[l,c]}function oo({defaultProp:e,onChange:t}){const[n,o]=u.useState(e),r=u.useRef(n),s=u.useRef(t);return to(()=>{s.current=t},[t]),u.useEffect(()=>{r.current!==n&&(s.current?.(n),r.current=n)},[n,r]),[n,o,s]}function ro(e){return typeof e=="function"}var so=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),io="VisuallyHidden",Oe=u.forwardRef((e,t)=>b.jsx($.span,{...e,ref:t,style:{...so,...e.style}}));Oe.displayName=io;var co=Oe,Ze="ToastProvider",[Je,ao,lo]=Wn("Toast"),[Lt,Qr]=Se("Toast",[lo]),[uo,De]=Lt(Ze),_t=e=>{const{__scopeToast:t,label:n="Notification",duration:o=5e3,swipeDirection:r="right",swipeThreshold:s=50,children:i}=e,[a,l]=u.useState(null),[c,f]=u.useState(0),d=u.useRef(!1),v=u.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${Ze}\`. Expected non-empty \`string\`.`),b.jsx(Je.Provider,{scope:t,children:b.jsx(uo,{scope:t,label:n,duration:o,swipeDirection:r,swipeThreshold:s,toastCount:c,viewport:a,onViewportChange:l,onToastAdd:u.useCallback(()=>f(p=>p+1),[]),onToastRemove:u.useCallback(()=>f(p=>p-1),[]),isFocusedToastEscapeKeyDownRef:d,isClosePausedRef:v,children:i})})};_t.displayName=Ze;var Mt="ToastViewport",fo=["F8"],He="toast.viewportPause",Be="toast.viewportResume",It=u.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:o=fo,label:r="Notifications ({hotkey})",...s}=e,i=De(Mt,n),a=ao(n),l=u.useRef(null),c=u.useRef(null),f=u.useRef(null),d=u.useRef(null),v=V(t,d,i.onViewportChange),p=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),g=i.toastCount>0;u.useEffect(()=>{const m=y=>{o.length!==0&&o.every(x=>y[x]||y.code===x)&&d.current?.focus()};return document.addEventListener("keydown",m),()=>document.removeEventListener("keydown",m)},[o]),u.useEffect(()=>{const m=l.current,y=d.current;if(g&&m&&y){const w=()=>{if(!i.isClosePausedRef.current){const R=new CustomEvent(He);y.dispatchEvent(R),i.isClosePausedRef.current=!0}},x=()=>{if(i.isClosePausedRef.current){const R=new CustomEvent(Be);y.dispatchEvent(R),i.isClosePausedRef.current=!1}},E=R=>{!m.contains(R.relatedTarget)&&x()},T=()=>{m.contains(document.activeElement)||x()};return m.addEventListener("focusin",w),m.addEventListener("focusout",E),m.addEventListener("pointermove",w),m.addEventListener("pointerleave",T),window.addEventListener("blur",w),window.addEventListener("focus",x),()=>{m.removeEventListener("focusin",w),m.removeEventListener("focusout",E),m.removeEventListener("pointermove",w),m.removeEventListener("pointerleave",T),window.removeEventListener("blur",w),window.removeEventListener("focus",x)}}},[g,i.isClosePausedRef]);const h=u.useCallback(({tabbingDirection:m})=>{const w=a().map(x=>{const E=x.ref.current,T=[E,...Po(E)];return m==="forwards"?T:T.reverse()});return(m==="forwards"?w.reverse():w).flat()},[a]);return u.useEffect(()=>{const m=d.current;if(m){const y=w=>{const x=w.altKey||w.ctrlKey||w.metaKey;if(w.key==="Tab"&&!x){const T=document.activeElement,R=w.shiftKey;if(w.target===m&&R){c.current?.focus();return}const L=h({tabbingDirection:R?"backwards":"forwards"}),D=L.findIndex(_=>_===T);Fe(L.slice(D+1))?w.preventDefault():R?c.current?.focus():f.current?.focus()}};return m.addEventListener("keydown",y),()=>m.removeEventListener("keydown",y)}},[a,h]),b.jsxs(Gn,{ref:l,role:"region","aria-label":r.replace("{hotkey}",p),tabIndex:-1,style:{pointerEvents:g?void 0:"none"},children:[g&&b.jsx(Ve,{ref:c,onFocusFromOutsideViewport:()=>{const m=h({tabbingDirection:"forwards"});Fe(m)}}),b.jsx(Je.Slot,{scope:n,children:b.jsx($.ol,{tabIndex:-1,...s,ref:v})}),g&&b.jsx(Ve,{ref:f,onFocusFromOutsideViewport:()=>{const m=h({tabbingDirection:"backwards"});Fe(m)}})]})});It.displayName=Mt;var Ft="ToastFocusProxy",Ve=u.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:o,...r}=e,s=De(Ft,n);return b.jsx(Oe,{"aria-hidden":!0,tabIndex:0,...r,ref:t,style:{position:"fixed"},onFocus:i=>{const a=i.relatedTarget;!s.viewport?.contains(a)&&o()}})});Ve.displayName=Ft;var me="Toast",po="toast.swipeStart",mo="toast.swipeMove",ho="toast.swipeCancel",vo="toast.swipeEnd",kt=u.forwardRef((e,t)=>{const{forceMount:n,open:o,defaultOpen:r,onOpenChange:s,...i}=e,[a,l]=no({prop:o,defaultProp:r??!0,onChange:s,caller:me});return b.jsx(Ge,{present:n||a,children:b.jsx(yo,{open:a,...i,ref:t,onClose:()=>l(!1),onPause:J(e.onPause),onResume:J(e.onResume),onSwipeStart:j(e.onSwipeStart,c=>{c.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:j(e.onSwipeMove,c=>{const{x:f,y:d}=c.detail.delta;c.currentTarget.setAttribute("data-swipe","move"),c.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${f}px`),c.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${d}px`)}),onSwipeCancel:j(e.onSwipeCancel,c=>{c.currentTarget.setAttribute("data-swipe","cancel"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),c.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),c.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:j(e.onSwipeEnd,c=>{const{x:f,y:d}=c.detail.delta;c.currentTarget.setAttribute("data-swipe","end"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),c.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${f}px`),c.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${d}px`),l(!1)})})})});kt.displayName=me;var[go,wo]=Lt(me,{onClose(){}}),yo=u.forwardRef((e,t)=>{const{__scopeToast:n,type:o="foreground",duration:r,open:s,onClose:i,onEscapeKeyDown:a,onPause:l,onResume:c,onSwipeStart:f,onSwipeMove:d,onSwipeCancel:v,onSwipeEnd:p,...g}=e,h=De(me,n),[m,y]=u.useState(null),w=V(t,C=>y(C)),x=u.useRef(null),E=u.useRef(null),T=r||h.duration,R=u.useRef(0),P=u.useRef(T),O=u.useRef(0),{onToastAdd:L,onToastRemove:D}=h,_=J(()=>{m?.contains(document.activeElement)&&h.viewport?.focus(),i()}),k=u.useCallback(C=>{!C||C===1/0||(window.clearTimeout(O.current),R.current=new Date().getTime(),O.current=window.setTimeout(_,C))},[_]);u.useEffect(()=>{const C=h.viewport;if(C){const M=()=>{k(P.current),c?.()},S=()=>{const N=new Date().getTime()-R.current;P.current=P.current-N,window.clearTimeout(O.current),l?.()};return C.addEventListener(He,S),C.addEventListener(Be,M),()=>{C.removeEventListener(He,S),C.removeEventListener(Be,M)}}},[h.viewport,T,l,c,k]),u.useEffect(()=>{s&&!h.isClosePausedRef.current&&k(T)},[s,T,h.isClosePausedRef,k]),u.useEffect(()=>(L(),()=>D()),[L,D]);const I=u.useMemo(()=>m?zt(m):null,[m]);return h.viewport?b.jsxs(b.Fragment,{children:[I&&b.jsx(xo,{__scopeToast:n,role:"status","aria-live":o==="foreground"?"assertive":"polite","aria-atomic":!0,children:I}),b.jsx(go,{scope:n,onClose:_,children:Xe.createPortal(b.jsx(Je.ItemSlot,{scope:n,children:b.jsx(qn,{asChild:!0,onEscapeKeyDown:j(a,()=>{h.isFocusedToastEscapeKeyDownRef.current||_(),h.isFocusedToastEscapeKeyDownRef.current=!1}),children:b.jsx($.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":s?"open":"closed","data-swipe-direction":h.swipeDirection,...g,ref:w,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:j(e.onKeyDown,C=>{C.key==="Escape"&&(a?.(C.nativeEvent),C.nativeEvent.defaultPrevented||(h.isFocusedToastEscapeKeyDownRef.current=!0,_()))}),onPointerDown:j(e.onPointerDown,C=>{C.button===0&&(x.current={x:C.clientX,y:C.clientY})}),onPointerMove:j(e.onPointerMove,C=>{if(!x.current)return;const M=C.clientX-x.current.x,S=C.clientY-x.current.y,N=!!E.current,A=["left","right"].includes(h.swipeDirection),F=["left","up"].includes(h.swipeDirection)?Math.min:Math.max,B=A?F(0,M):0,ve=A?0:F(0,S),ue=C.pointerType==="touch"?10:2,re={x:B,y:ve},ge={originalEvent:C,delta:re};N?(E.current=re,xe(mo,d,ge,{discrete:!1})):pt(re,h.swipeDirection,ue)?(E.current=re,xe(po,f,ge,{discrete:!1}),C.target.setPointerCapture(C.pointerId)):(Math.abs(M)>ue||Math.abs(S)>ue)&&(x.current=null)}),onPointerUp:j(e.onPointerUp,C=>{const M=E.current,S=C.target;if(S.hasPointerCapture(C.pointerId)&&S.releasePointerCapture(C.pointerId),E.current=null,x.current=null,M){const N=C.currentTarget,A={originalEvent:C,delta:M};pt(M,h.swipeDirection,h.swipeThreshold)?xe(vo,p,A,{discrete:!0}):xe(ho,v,A,{discrete:!0}),N.addEventListener("click",F=>F.preventDefault(),{once:!0})}})})})}),h.viewport)})]}):null}),xo=e=>{const{__scopeToast:t,children:n,...o}=e,r=De(me,t),[s,i]=u.useState(!1),[a,l]=u.useState(!1);return To(()=>i(!0)),u.useEffect(()=>{const c=window.setTimeout(()=>l(!0),1e3);return()=>window.clearTimeout(c)},[]),a?null:b.jsx(Nt,{asChild:!0,children:b.jsx(Oe,{...o,children:s&&b.jsxs(b.Fragment,{children:[r.label," ",n]})})})},Eo="ToastTitle",jt=u.forwardRef((e,t)=>{const{__scopeToast:n,...o}=e;return b.jsx($.div,{...o,ref:t})});jt.displayName=Eo;var bo="ToastDescription",$t=u.forwardRef((e,t)=>{const{__scopeToast:n,...o}=e;return b.jsx($.div,{...o,ref:t})});$t.displayName=bo;var Wt="ToastAction",Ht=u.forwardRef((e,t)=>{const{altText:n,...o}=e;return n.trim()?b.jsx(Vt,{altText:n,asChild:!0,children:b.jsx(Qe,{...o,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${Wt}\`. Expected non-empty \`string\`.`),null)});Ht.displayName=Wt;var Bt="ToastClose",Qe=u.forwardRef((e,t)=>{const{__scopeToast:n,...o}=e,r=wo(Bt,n);return b.jsx(Vt,{asChild:!0,children:b.jsx($.button,{type:"button",...o,ref:t,onClick:j(e.onClick,r.onClose)})})});Qe.displayName=Bt;var Vt=u.forwardRef((e,t)=>{const{__scopeToast:n,altText:o,...r}=e;return b.jsx($.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":o||void 0,...r,ref:t})});function zt(e){const t=[];return Array.from(e.childNodes).forEach(o=>{if(o.nodeType===o.TEXT_NODE&&o.textContent&&t.push(o.textContent),Co(o)){const r=o.ariaHidden||o.hidden||o.style.display==="none",s=o.dataset.radixToastAnnounceExclude==="";if(!r)if(s){const i=o.dataset.radixToastAnnounceAlt;i&&t.push(i)}else t.push(...zt(o))}}),t}function xe(e,t,n,{discrete:o}){const r=n.originalEvent.currentTarget,s=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&r.addEventListener(e,t,{once:!0}),o?At(r,s):r.dispatchEvent(s)}var pt=(e,t,n=0)=>{const o=Math.abs(e.x),r=Math.abs(e.y),s=o>r;return t==="left"||t==="right"?s&&o>n:!s&&r>n};function To(e=()=>{}){const t=J(e);Q(()=>{let n=0,o=0;return n=window.requestAnimationFrame(()=>o=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(o)}},[t])}function Co(e){return e.nodeType===e.ELEMENT_NODE}function Po(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:o=>{const r=o.tagName==="INPUT"&&o.type==="hidden";return o.disabled||o.hidden||r?NodeFilter.FILTER_SKIP:o.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Fe(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var es=_t,ts=It,ns=kt,os=jt,rs=$t,ss=Ht,is=Qe;const Ro=["top","right","bottom","left"],ee=Math.min,W=Math.max,Te=Math.round,Ee=Math.floor,Y=e=>({x:e,y:e}),Ao={left:"right",right:"left",bottom:"top",top:"bottom"},So={start:"end",end:"start"};function ze(e,t,n){return W(e,ee(t,n))}function G(e,t){return typeof e=="function"?e(t):e}function Z(e){return e.split("-")[0]}function ae(e){return e.split("-")[1]}function et(e){return e==="x"?"y":"x"}function tt(e){return e==="y"?"height":"width"}function K(e){return["top","bottom"].includes(Z(e))?"y":"x"}function nt(e){return et(K(e))}function Oo(e,t,n){n===void 0&&(n=!1);const o=ae(e),r=nt(e),s=tt(r);let i=r==="x"?o===(n?"end":"start")?"right":"left":o==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(i=Ce(i)),[i,Ce(i)]}function Do(e){const t=Ce(e);return[Ue(e),t,Ue(t)]}function Ue(e){return e.replace(/start|end/g,t=>So[t])}function No(e,t,n){const o=["left","right"],r=["right","left"],s=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?r:o:t?o:r;case"left":case"right":return t?s:i;default:return[]}}function Lo(e,t,n,o){const r=ae(e);let s=No(Z(e),n==="start",o);return r&&(s=s.map(i=>i+"-"+r),t&&(s=s.concat(s.map(Ue)))),s}function Ce(e){return e.replace(/left|right|bottom|top/g,t=>Ao[t])}function _o(e){return{top:0,right:0,bottom:0,left:0,...e}}function Ut(e){return typeof e!="number"?_o(e):{top:e,right:e,bottom:e,left:e}}function Pe(e){const{x:t,y:n,width:o,height:r}=e;return{width:o,height:r,top:n,left:t,right:t+o,bottom:n+r,x:t,y:n}}function mt(e,t,n){let{reference:o,floating:r}=e;const s=K(t),i=nt(t),a=tt(i),l=Z(t),c=s==="y",f=o.x+o.width/2-r.width/2,d=o.y+o.height/2-r.height/2,v=o[a]/2-r[a]/2;let p;switch(l){case"top":p={x:f,y:o.y-r.height};break;case"bottom":p={x:f,y:o.y+o.height};break;case"right":p={x:o.x+o.width,y:d};break;case"left":p={x:o.x-r.width,y:d};break;default:p={x:o.x,y:o.y}}switch(ae(t)){case"start":p[i]-=v*(n&&c?-1:1);break;case"end":p[i]+=v*(n&&c?-1:1);break}return p}const Mo=async(e,t,n)=>{const{placement:o="bottom",strategy:r="absolute",middleware:s=[],platform:i}=n,a=s.filter(Boolean),l=await(i.isRTL==null?void 0:i.isRTL(t));let c=await i.getElementRects({reference:e,floating:t,strategy:r}),{x:f,y:d}=mt(c,o,l),v=o,p={},g=0;for(let h=0;h<a.length;h++){const{name:m,fn:y}=a[h],{x:w,y:x,data:E,reset:T}=await y({x:f,y:d,initialPlacement:o,placement:v,strategy:r,middlewareData:p,rects:c,platform:i,elements:{reference:e,floating:t}});f=w??f,d=x??d,p={...p,[m]:{...p[m],...E}},T&&g<=50&&(g++,typeof T=="object"&&(T.placement&&(v=T.placement),T.rects&&(c=T.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:r}):T.rects),{x:f,y:d}=mt(c,v,l)),h=-1)}return{x:f,y:d,placement:v,strategy:r,middlewareData:p}};async function de(e,t){var n;t===void 0&&(t={});const{x:o,y:r,platform:s,rects:i,elements:a,strategy:l}=e,{boundary:c="clippingAncestors",rootBoundary:f="viewport",elementContext:d="floating",altBoundary:v=!1,padding:p=0}=G(t,e),g=Ut(p),m=a[v?d==="floating"?"reference":"floating":d],y=Pe(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(m)))==null||n?m:m.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(a.floating)),boundary:c,rootBoundary:f,strategy:l})),w=d==="floating"?{x:o,y:r,width:i.floating.width,height:i.floating.height}:i.reference,x=await(s.getOffsetParent==null?void 0:s.getOffsetParent(a.floating)),E=await(s.isElement==null?void 0:s.isElement(x))?await(s.getScale==null?void 0:s.getScale(x))||{x:1,y:1}:{x:1,y:1},T=Pe(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:x,strategy:l}):w);return{top:(y.top-T.top+g.top)/E.y,bottom:(T.bottom-y.bottom+g.bottom)/E.y,left:(y.left-T.left+g.left)/E.x,right:(T.right-y.right+g.right)/E.x}}const Io=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:o,placement:r,rects:s,platform:i,elements:a,middlewareData:l}=t,{element:c,padding:f=0}=G(e,t)||{};if(c==null)return{};const d=Ut(f),v={x:n,y:o},p=nt(r),g=tt(p),h=await i.getDimensions(c),m=p==="y",y=m?"top":"left",w=m?"bottom":"right",x=m?"clientHeight":"clientWidth",E=s.reference[g]+s.reference[p]-v[p]-s.floating[g],T=v[p]-s.reference[p],R=await(i.getOffsetParent==null?void 0:i.getOffsetParent(c));let P=R?R[x]:0;(!P||!await(i.isElement==null?void 0:i.isElement(R)))&&(P=a.floating[x]||s.floating[g]);const O=E/2-T/2,L=P/2-h[g]/2-1,D=ee(d[y],L),_=ee(d[w],L),k=D,I=P-h[g]-_,C=P/2-h[g]/2+O,M=ze(k,C,I),S=!l.arrow&&ae(r)!=null&&C!==M&&s.reference[g]/2-(C<k?D:_)-h[g]/2<0,N=S?C<k?C-k:C-I:0;return{[p]:v[p]+N,data:{[p]:M,centerOffset:C-M-N,...S&&{alignmentOffset:N}},reset:S}}}),Fo=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,o;const{placement:r,middlewareData:s,rects:i,initialPlacement:a,platform:l,elements:c}=t,{mainAxis:f=!0,crossAxis:d=!0,fallbackPlacements:v,fallbackStrategy:p="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:h=!0,...m}=G(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const y=Z(r),w=K(a),x=Z(a)===a,E=await(l.isRTL==null?void 0:l.isRTL(c.floating)),T=v||(x||!h?[Ce(a)]:Do(a)),R=g!=="none";!v&&R&&T.push(...Lo(a,h,g,E));const P=[a,...T],O=await de(t,m),L=[];let D=((o=s.flip)==null?void 0:o.overflows)||[];if(f&&L.push(O[y]),d){const C=Oo(r,i,E);L.push(O[C[0]],O[C[1]])}if(D=[...D,{placement:r,overflows:L}],!L.every(C=>C<=0)){var _,k;const C=(((_=s.flip)==null?void 0:_.index)||0)+1,M=P[C];if(M&&(!(d==="alignment"?w!==K(M):!1)||D.every(A=>A.overflows[0]>0&&K(A.placement)===w)))return{data:{index:C,overflows:D},reset:{placement:M}};let S=(k=D.filter(N=>N.overflows[0]<=0).sort((N,A)=>N.overflows[1]-A.overflows[1])[0])==null?void 0:k.placement;if(!S)switch(p){case"bestFit":{var I;const N=(I=D.filter(A=>{if(R){const F=K(A.placement);return F===w||F==="y"}return!0}).map(A=>[A.placement,A.overflows.filter(F=>F>0).reduce((F,B)=>F+B,0)]).sort((A,F)=>A[1]-F[1])[0])==null?void 0:I[0];N&&(S=N);break}case"initialPlacement":S=a;break}if(r!==S)return{reset:{placement:S}}}return{}}}};function ht(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function vt(e){return Ro.some(t=>e[t]>=0)}const ko=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:o="referenceHidden",...r}=G(e,t);switch(o){case"referenceHidden":{const s=await de(t,{...r,elementContext:"reference"}),i=ht(s,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:vt(i)}}}case"escaped":{const s=await de(t,{...r,altBoundary:!0}),i=ht(s,n.floating);return{data:{escapedOffsets:i,escaped:vt(i)}}}default:return{}}}}};async function jo(e,t){const{placement:n,platform:o,elements:r}=e,s=await(o.isRTL==null?void 0:o.isRTL(r.floating)),i=Z(n),a=ae(n),l=K(n)==="y",c=["left","top"].includes(i)?-1:1,f=s&&l?-1:1,d=G(t,e);let{mainAxis:v,crossAxis:p,alignmentAxis:g}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&typeof g=="number"&&(p=a==="end"?g*-1:g),l?{x:p*f,y:v*c}:{x:v*c,y:p*f}}const $o=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,o;const{x:r,y:s,placement:i,middlewareData:a}=t,l=await jo(t,e);return i===((n=a.offset)==null?void 0:n.placement)&&(o=a.arrow)!=null&&o.alignmentOffset?{}:{x:r+l.x,y:s+l.y,data:{...l,placement:i}}}}},Wo=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:o,placement:r}=t,{mainAxis:s=!0,crossAxis:i=!1,limiter:a={fn:m=>{let{x:y,y:w}=m;return{x:y,y:w}}},...l}=G(e,t),c={x:n,y:o},f=await de(t,l),d=K(Z(r)),v=et(d);let p=c[v],g=c[d];if(s){const m=v==="y"?"top":"left",y=v==="y"?"bottom":"right",w=p+f[m],x=p-f[y];p=ze(w,p,x)}if(i){const m=d==="y"?"top":"left",y=d==="y"?"bottom":"right",w=g+f[m],x=g-f[y];g=ze(w,g,x)}const h=a.fn({...t,[v]:p,[d]:g});return{...h,data:{x:h.x-n,y:h.y-o,enabled:{[v]:s,[d]:i}}}}}},Ho=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:o,placement:r,rects:s,middlewareData:i}=t,{offset:a=0,mainAxis:l=!0,crossAxis:c=!0}=G(e,t),f={x:n,y:o},d=K(r),v=et(d);let p=f[v],g=f[d];const h=G(a,t),m=typeof h=="number"?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(l){const x=v==="y"?"height":"width",E=s.reference[v]-s.floating[x]+m.mainAxis,T=s.reference[v]+s.reference[x]-m.mainAxis;p<E?p=E:p>T&&(p=T)}if(c){var y,w;const x=v==="y"?"width":"height",E=["top","left"].includes(Z(r)),T=s.reference[d]-s.floating[x]+(E&&((y=i.offset)==null?void 0:y[d])||0)+(E?0:m.crossAxis),R=s.reference[d]+s.reference[x]+(E?0:((w=i.offset)==null?void 0:w[d])||0)-(E?m.crossAxis:0);g<T?g=T:g>R&&(g=R)}return{[v]:p,[d]:g}}}},Bo=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,o;const{placement:r,rects:s,platform:i,elements:a}=t,{apply:l=()=>{},...c}=G(e,t),f=await de(t,c),d=Z(r),v=ae(r),p=K(r)==="y",{width:g,height:h}=s.floating;let m,y;d==="top"||d==="bottom"?(m=d,y=v===(await(i.isRTL==null?void 0:i.isRTL(a.floating))?"start":"end")?"left":"right"):(y=d,m=v==="end"?"top":"bottom");const w=h-f.top-f.bottom,x=g-f.left-f.right,E=ee(h-f[m],w),T=ee(g-f[y],x),R=!t.middlewareData.shift;let P=E,O=T;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(O=x),(o=t.middlewareData.shift)!=null&&o.enabled.y&&(P=w),R&&!v){const D=W(f.left,0),_=W(f.right,0),k=W(f.top,0),I=W(f.bottom,0);p?O=g-2*(D!==0||_!==0?D+_:W(f.left,f.right)):P=h-2*(k!==0||I!==0?k+I:W(f.top,f.bottom))}await l({...t,availableWidth:O,availableHeight:P});const L=await i.getDimensions(a.floating);return g!==L.width||h!==L.height?{reset:{rects:!0}}:{}}}};function Ne(){return typeof window<"u"}function le(e){return Kt(e)?(e.nodeName||"").toLowerCase():"#document"}function H(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function q(e){var t;return(t=(Kt(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Kt(e){return Ne()?e instanceof Node||e instanceof H(e).Node:!1}function z(e){return Ne()?e instanceof Element||e instanceof H(e).Element:!1}function X(e){return Ne()?e instanceof HTMLElement||e instanceof H(e).HTMLElement:!1}function gt(e){return!Ne()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof H(e).ShadowRoot}function he(e){const{overflow:t,overflowX:n,overflowY:o,display:r}=U(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!["inline","contents"].includes(r)}function Vo(e){return["table","td","th"].includes(le(e))}function Le(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function ot(e){const t=rt(),n=z(e)?U(e):e;return["transform","translate","scale","rotate","perspective"].some(o=>n[o]?n[o]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(o=>(n.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(n.contain||"").includes(o))}function zo(e){let t=te(e);for(;X(t)&&!ie(t);){if(ot(t))return t;if(Le(t))return null;t=te(t)}return null}function rt(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function ie(e){return["html","body","#document"].includes(le(e))}function U(e){return H(e).getComputedStyle(e)}function _e(e){return z(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function te(e){if(le(e)==="html")return e;const t=e.assignedSlot||e.parentNode||gt(e)&&e.host||q(e);return gt(t)?t.host:t}function Yt(e){const t=te(e);return ie(t)?e.ownerDocument?e.ownerDocument.body:e.body:X(t)&&he(t)?t:Yt(t)}function pe(e,t,n){var o;t===void 0&&(t=[]),n===void 0&&(n=!0);const r=Yt(e),s=r===((o=e.ownerDocument)==null?void 0:o.body),i=H(r);if(s){const a=Ke(i);return t.concat(i,i.visualViewport||[],he(r)?r:[],a&&n?pe(a):[])}return t.concat(r,pe(r,[],n))}function Ke(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Xt(e){const t=U(e);let n=parseFloat(t.width)||0,o=parseFloat(t.height)||0;const r=X(e),s=r?e.offsetWidth:n,i=r?e.offsetHeight:o,a=Te(n)!==s||Te(o)!==i;return a&&(n=s,o=i),{width:n,height:o,$:a}}function st(e){return z(e)?e:e.contextElement}function se(e){const t=st(e);if(!X(t))return Y(1);const n=t.getBoundingClientRect(),{width:o,height:r,$:s}=Xt(t);let i=(s?Te(n.width):n.width)/o,a=(s?Te(n.height):n.height)/r;return(!i||!Number.isFinite(i))&&(i=1),(!a||!Number.isFinite(a))&&(a=1),{x:i,y:a}}const Uo=Y(0);function qt(e){const t=H(e);return!rt()||!t.visualViewport?Uo:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Ko(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==H(e)?!1:t}function oe(e,t,n,o){t===void 0&&(t=!1),n===void 0&&(n=!1);const r=e.getBoundingClientRect(),s=st(e);let i=Y(1);t&&(o?z(o)&&(i=se(o)):i=se(e));const a=Ko(s,n,o)?qt(s):Y(0);let l=(r.left+a.x)/i.x,c=(r.top+a.y)/i.y,f=r.width/i.x,d=r.height/i.y;if(s){const v=H(s),p=o&&z(o)?H(o):o;let g=v,h=Ke(g);for(;h&&o&&p!==g;){const m=se(h),y=h.getBoundingClientRect(),w=U(h),x=y.left+(h.clientLeft+parseFloat(w.paddingLeft))*m.x,E=y.top+(h.clientTop+parseFloat(w.paddingTop))*m.y;l*=m.x,c*=m.y,f*=m.x,d*=m.y,l+=x,c+=E,g=H(h),h=Ke(g)}}return Pe({width:f,height:d,x:l,y:c})}function it(e,t){const n=_e(e).scrollLeft;return t?t.left+n:oe(q(e)).left+n}function Gt(e,t,n){n===void 0&&(n=!1);const o=e.getBoundingClientRect(),r=o.left+t.scrollLeft-(n?0:it(e,o)),s=o.top+t.scrollTop;return{x:r,y:s}}function Yo(e){let{elements:t,rect:n,offsetParent:o,strategy:r}=e;const s=r==="fixed",i=q(o),a=t?Le(t.floating):!1;if(o===i||a&&s)return n;let l={scrollLeft:0,scrollTop:0},c=Y(1);const f=Y(0),d=X(o);if((d||!d&&!s)&&((le(o)!=="body"||he(i))&&(l=_e(o)),X(o))){const p=oe(o);c=se(o),f.x=p.x+o.clientLeft,f.y=p.y+o.clientTop}const v=i&&!d&&!s?Gt(i,l,!0):Y(0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-l.scrollLeft*c.x+f.x+v.x,y:n.y*c.y-l.scrollTop*c.y+f.y+v.y}}function Xo(e){return Array.from(e.getClientRects())}function qo(e){const t=q(e),n=_e(e),o=e.ownerDocument.body,r=W(t.scrollWidth,t.clientWidth,o.scrollWidth,o.clientWidth),s=W(t.scrollHeight,t.clientHeight,o.scrollHeight,o.clientHeight);let i=-n.scrollLeft+it(e);const a=-n.scrollTop;return U(o).direction==="rtl"&&(i+=W(t.clientWidth,o.clientWidth)-r),{width:r,height:s,x:i,y:a}}function Go(e,t){const n=H(e),o=q(e),r=n.visualViewport;let s=o.clientWidth,i=o.clientHeight,a=0,l=0;if(r){s=r.width,i=r.height;const c=rt();(!c||c&&t==="fixed")&&(a=r.offsetLeft,l=r.offsetTop)}return{width:s,height:i,x:a,y:l}}function Zo(e,t){const n=oe(e,!0,t==="fixed"),o=n.top+e.clientTop,r=n.left+e.clientLeft,s=X(e)?se(e):Y(1),i=e.clientWidth*s.x,a=e.clientHeight*s.y,l=r*s.x,c=o*s.y;return{width:i,height:a,x:l,y:c}}function wt(e,t,n){let o;if(t==="viewport")o=Go(e,n);else if(t==="document")o=qo(q(e));else if(z(t))o=Zo(t,n);else{const r=qt(e);o={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return Pe(o)}function Zt(e,t){const n=te(e);return n===t||!z(n)||ie(n)?!1:U(n).position==="fixed"||Zt(n,t)}function Jo(e,t){const n=t.get(e);if(n)return n;let o=pe(e,[],!1).filter(a=>z(a)&&le(a)!=="body"),r=null;const s=U(e).position==="fixed";let i=s?te(e):e;for(;z(i)&&!ie(i);){const a=U(i),l=ot(i);!l&&a.position==="fixed"&&(r=null),(s?!l&&!r:!l&&a.position==="static"&&!!r&&["absolute","fixed"].includes(r.position)||he(i)&&!l&&Zt(e,i))?o=o.filter(f=>f!==i):r=a,i=te(i)}return t.set(e,o),o}function Qo(e){let{element:t,boundary:n,rootBoundary:o,strategy:r}=e;const i=[...n==="clippingAncestors"?Le(t)?[]:Jo(t,this._c):[].concat(n),o],a=i[0],l=i.reduce((c,f)=>{const d=wt(t,f,r);return c.top=W(d.top,c.top),c.right=ee(d.right,c.right),c.bottom=ee(d.bottom,c.bottom),c.left=W(d.left,c.left),c},wt(t,a,r));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function er(e){const{width:t,height:n}=Xt(e);return{width:t,height:n}}function tr(e,t,n){const o=X(t),r=q(t),s=n==="fixed",i=oe(e,!0,s,t);let a={scrollLeft:0,scrollTop:0};const l=Y(0);function c(){l.x=it(r)}if(o||!o&&!s)if((le(t)!=="body"||he(r))&&(a=_e(t)),o){const p=oe(t,!0,s,t);l.x=p.x+t.clientLeft,l.y=p.y+t.clientTop}else r&&c();s&&!o&&r&&c();const f=r&&!o&&!s?Gt(r,a):Y(0),d=i.left+a.scrollLeft-l.x-f.x,v=i.top+a.scrollTop-l.y-f.y;return{x:d,y:v,width:i.width,height:i.height}}function ke(e){return U(e).position==="static"}function yt(e,t){if(!X(e)||U(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return q(e)===n&&(n=n.ownerDocument.body),n}function Jt(e,t){const n=H(e);if(Le(e))return n;if(!X(e)){let r=te(e);for(;r&&!ie(r);){if(z(r)&&!ke(r))return r;r=te(r)}return n}let o=yt(e,t);for(;o&&Vo(o)&&ke(o);)o=yt(o,t);return o&&ie(o)&&ke(o)&&!ot(o)?n:o||zo(e)||n}const nr=async function(e){const t=this.getOffsetParent||Jt,n=this.getDimensions,o=await n(e.floating);return{reference:tr(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function or(e){return U(e).direction==="rtl"}const rr={convertOffsetParentRelativeRectToViewportRelativeRect:Yo,getDocumentElement:q,getClippingRect:Qo,getOffsetParent:Jt,getElementRects:nr,getClientRects:Xo,getDimensions:er,getScale:se,isElement:z,isRTL:or};function Qt(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function sr(e,t){let n=null,o;const r=q(e);function s(){var a;clearTimeout(o),(a=n)==null||a.disconnect(),n=null}function i(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),s();const c=e.getBoundingClientRect(),{left:f,top:d,width:v,height:p}=c;if(a||t(),!v||!p)return;const g=Ee(d),h=Ee(r.clientWidth-(f+v)),m=Ee(r.clientHeight-(d+p)),y=Ee(f),x={rootMargin:-g+"px "+-h+"px "+-m+"px "+-y+"px",threshold:W(0,ee(1,l))||1};let E=!0;function T(R){const P=R[0].intersectionRatio;if(P!==l){if(!E)return i();P?i(!1,P):o=setTimeout(()=>{i(!1,1e-7)},1e3)}P===1&&!Qt(c,e.getBoundingClientRect())&&i(),E=!1}try{n=new IntersectionObserver(T,{...x,root:r.ownerDocument})}catch{n=new IntersectionObserver(T,x)}n.observe(e)}return i(!0),s}function ir(e,t,n,o){o===void 0&&(o={});const{ancestorScroll:r=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=o,c=st(e),f=r||s?[...c?pe(c):[],...pe(t)]:[];f.forEach(y=>{r&&y.addEventListener("scroll",n,{passive:!0}),s&&y.addEventListener("resize",n)});const d=c&&a?sr(c,n):null;let v=-1,p=null;i&&(p=new ResizeObserver(y=>{let[w]=y;w&&w.target===c&&p&&(p.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var x;(x=p)==null||x.observe(t)})),n()}),c&&!l&&p.observe(c),p.observe(t));let g,h=l?oe(e):null;l&&m();function m(){const y=oe(e);h&&!Qt(h,y)&&n(),h=y,g=requestAnimationFrame(m)}return n(),()=>{var y;f.forEach(w=>{r&&w.removeEventListener("scroll",n),s&&w.removeEventListener("resize",n)}),d?.(),(y=p)==null||y.disconnect(),p=null,l&&cancelAnimationFrame(g)}}const cr=$o,ar=Wo,lr=Fo,ur=Bo,fr=ko,xt=Io,dr=Ho,pr=(e,t,n)=>{const o=new Map,r={platform:rr,...n},s={...r.platform,_c:o};return Mo(e,t,{...r,platform:s})};var mr=typeof document<"u",hr=function(){},be=mr?u.useLayoutEffect:hr;function Re(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,o,r;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(o=n;o--!==0;)if(!Re(e[o],t[o]))return!1;return!0}if(r=Object.keys(e),n=r.length,n!==Object.keys(t).length)return!1;for(o=n;o--!==0;)if(!{}.hasOwnProperty.call(t,r[o]))return!1;for(o=n;o--!==0;){const s=r[o];if(!(s==="_owner"&&e.$$typeof)&&!Re(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function en(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Et(e,t){const n=en(e);return Math.round(t*n)/n}function je(e){const t=u.useRef(e);return be(()=>{t.current=e}),t}function vr(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:r,elements:{reference:s,floating:i}={},transform:a=!0,whileElementsMounted:l,open:c}=e,[f,d]=u.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[v,p]=u.useState(o);Re(v,o)||p(o);const[g,h]=u.useState(null),[m,y]=u.useState(null),w=u.useCallback(A=>{A!==R.current&&(R.current=A,h(A))},[]),x=u.useCallback(A=>{A!==P.current&&(P.current=A,y(A))},[]),E=s||g,T=i||m,R=u.useRef(null),P=u.useRef(null),O=u.useRef(f),L=l!=null,D=je(l),_=je(r),k=je(c),I=u.useCallback(()=>{if(!R.current||!P.current)return;const A={placement:t,strategy:n,middleware:v};_.current&&(A.platform=_.current),pr(R.current,P.current,A).then(F=>{const B={...F,isPositioned:k.current!==!1};C.current&&!Re(O.current,B)&&(O.current=B,Xe.flushSync(()=>{d(B)}))})},[v,t,n,_,k]);be(()=>{c===!1&&O.current.isPositioned&&(O.current.isPositioned=!1,d(A=>({...A,isPositioned:!1})))},[c]);const C=u.useRef(!1);be(()=>(C.current=!0,()=>{C.current=!1}),[]),be(()=>{if(E&&(R.current=E),T&&(P.current=T),E&&T){if(D.current)return D.current(E,T,I);I()}},[E,T,I,D,L]);const M=u.useMemo(()=>({reference:R,floating:P,setReference:w,setFloating:x}),[w,x]),S=u.useMemo(()=>({reference:E,floating:T}),[E,T]),N=u.useMemo(()=>{const A={position:n,left:0,top:0};if(!S.floating)return A;const F=Et(S.floating,f.x),B=Et(S.floating,f.y);return a?{...A,transform:"translate("+F+"px, "+B+"px)",...en(S.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:F,top:B}},[n,a,S.floating,f.x,f.y]);return u.useMemo(()=>({...f,update:I,refs:M,elements:S,floatingStyles:N}),[f,I,M,S,N])}const gr=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:o,padding:r}=typeof e=="function"?e(n):e;return o&&t(o)?o.current!=null?xt({element:o.current,padding:r}).fn(n):{}:o?xt({element:o,padding:r}).fn(n):{}}}},wr=(e,t)=>({...cr(e),options:[e,t]}),yr=(e,t)=>({...ar(e),options:[e,t]}),xr=(e,t)=>({...dr(e),options:[e,t]}),Er=(e,t)=>({...lr(e),options:[e,t]}),br=(e,t)=>({...ur(e),options:[e,t]}),Tr=(e,t)=>({...fr(e),options:[e,t]}),Cr=(e,t)=>({...gr(e),options:[e,t]});var Pr="Arrow",tn=u.forwardRef((e,t)=>{const{children:n,width:o=10,height:r=5,...s}=e;return b.jsx($.svg,{...s,ref:t,width:o,height:r,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:b.jsx("polygon",{points:"0,0 30,0 15,10"})})});tn.displayName=Pr;var Rr=tn;function Ar(e){const[t,n]=u.useState(void 0);return Q(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const o=new ResizeObserver(r=>{if(!Array.isArray(r)||!r.length)return;const s=r[0];let i,a;if("borderBoxSize"in s){const l=s.borderBoxSize,c=Array.isArray(l)?l[0]:l;i=c.inlineSize,a=c.blockSize}else i=e.offsetWidth,a=e.offsetHeight;n({width:i,height:a})});return o.observe(e,{box:"border-box"}),()=>o.unobserve(e)}else n(void 0)},[e]),t}var nn="Popper",[on,rn]=Se(nn),[cs,sn]=on(nn),cn="PopperAnchor",an=u.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:o,...r}=e,s=sn(cn,n),i=u.useRef(null),a=V(t,i);return u.useEffect(()=>{s.onAnchorChange(o?.current||i.current)}),o?null:b.jsx($.div,{...r,ref:a})});an.displayName=cn;var ct="PopperContent",[Sr,Or]=on(ct),ln=u.forwardRef((e,t)=>{const{__scopePopper:n,side:o="bottom",sideOffset:r=0,align:s="center",alignOffset:i=0,arrowPadding:a=0,avoidCollisions:l=!0,collisionBoundary:c=[],collisionPadding:f=0,sticky:d="partial",hideWhenDetached:v=!1,updatePositionStrategy:p="optimized",onPlaced:g,...h}=e,m=sn(ct,n),[y,w]=u.useState(null),x=V(t,fe=>w(fe)),[E,T]=u.useState(null),R=Ar(E),P=R?.width??0,O=R?.height??0,L=o+(s!=="center"?"-"+s:""),D=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},_=Array.isArray(c)?c:[c],k=_.length>0,I={padding:D,boundary:_.filter(Nr),altBoundary:k},{refs:C,floatingStyles:M,placement:S,isPositioned:N,middlewareData:A}=vr({strategy:"fixed",placement:L,whileElementsMounted:(...fe)=>ir(...fe,{animationFrame:p==="always"}),elements:{reference:m.anchor},middleware:[wr({mainAxis:r+O,alignmentAxis:i}),l&&yr({mainAxis:!0,crossAxis:!1,limiter:d==="partial"?xr():void 0,...I}),l&&Er({...I}),br({...I,apply:({elements:fe,rects:lt,availableWidth:bn,availableHeight:Tn})=>{const{width:Cn,height:Pn}=lt.reference,we=fe.floating.style;we.setProperty("--radix-popper-available-width",`${bn}px`),we.setProperty("--radix-popper-available-height",`${Tn}px`),we.setProperty("--radix-popper-anchor-width",`${Cn}px`),we.setProperty("--radix-popper-anchor-height",`${Pn}px`)}}),E&&Cr({element:E,padding:a}),Lr({arrowWidth:P,arrowHeight:O}),v&&Tr({strategy:"referenceHidden",...I})]}),[F,B]=dn(S),ve=J(g);Q(()=>{N&&ve?.()},[N,ve]);const ue=A.arrow?.x,re=A.arrow?.y,ge=A.arrow?.centerOffset!==0,[xn,En]=u.useState();return Q(()=>{y&&En(window.getComputedStyle(y).zIndex)},[y]),b.jsx("div",{ref:C.setFloating,"data-radix-popper-content-wrapper":"",style:{...M,transform:N?M.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:xn,"--radix-popper-transform-origin":[A.transformOrigin?.x,A.transformOrigin?.y].join(" "),...A.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:b.jsx(Sr,{scope:n,placedSide:F,onArrowChange:T,arrowX:ue,arrowY:re,shouldHideArrow:ge,children:b.jsx($.div,{"data-side":F,"data-align":B,...h,ref:x,style:{...h.style,animation:N?void 0:"none"}})})})});ln.displayName=ct;var un="PopperArrow",Dr={top:"bottom",right:"left",bottom:"top",left:"right"},fn=u.forwardRef(function(t,n){const{__scopePopper:o,...r}=t,s=Or(un,o),i=Dr[s.placedSide];return b.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:b.jsx(Rr,{...r,ref:n,style:{...r.style,display:"block"}})})});fn.displayName=un;function Nr(e){return e!==null}var Lr=e=>({name:"transformOrigin",options:e,fn(t){const{placement:n,rects:o,middlewareData:r}=t,i=r.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[c,f]=dn(n),d={start:"0%",center:"50%",end:"100%"}[f],v=(r.arrow?.x??0)+a/2,p=(r.arrow?.y??0)+l/2;let g="",h="";return c==="bottom"?(g=i?d:`${v}px`,h=`${-l}px`):c==="top"?(g=i?d:`${v}px`,h=`${o.floating.height+l}px`):c==="right"?(g=`${-l}px`,h=i?d:`${p}px`):c==="left"&&(g=`${o.floating.width+l}px`,h=i?d:`${p}px`),{data:{x:g,y:h}}}});function dn(e){const[t,n="center"]=e.split("-");return[t,n]}var _r=an,Mr=ln,Ir=fn,[Me,as]=Se("Tooltip",[rn]),at=rn(),pn="TooltipProvider",Fr=700,bt="tooltip.open",[kr,mn]=Me(pn),hn=e=>{const{__scopeTooltip:t,delayDuration:n=Fr,skipDelayDuration:o=300,disableHoverableContent:r=!1,children:s}=e,i=u.useRef(!0),a=u.useRef(!1),l=u.useRef(0);return u.useEffect(()=>{const c=l.current;return()=>window.clearTimeout(c)},[]),b.jsx(kr,{scope:t,isOpenDelayedRef:i,delayDuration:n,onOpen:u.useCallback(()=>{window.clearTimeout(l.current),i.current=!1},[]),onClose:u.useCallback(()=>{window.clearTimeout(l.current),l.current=window.setTimeout(()=>i.current=!0,o)},[o]),isPointerInTransitRef:a,onPointerInTransitChange:u.useCallback(c=>{a.current=c},[]),disableHoverableContent:r,children:s})};hn.displayName=pn;var vn="Tooltip",[ls,Ie]=Me(vn),Ye="TooltipTrigger",jr=u.forwardRef((e,t)=>{const{__scopeTooltip:n,...o}=e,r=Ie(Ye,n),s=mn(Ye,n),i=at(n),a=u.useRef(null),l=V(t,a,r.onTriggerChange),c=u.useRef(!1),f=u.useRef(!1),d=u.useCallback(()=>c.current=!1,[]);return u.useEffect(()=>()=>document.removeEventListener("pointerup",d),[d]),b.jsx(_r,{asChild:!0,...i,children:b.jsx($.button,{"aria-describedby":r.open?r.contentId:void 0,"data-state":r.stateAttribute,...o,ref:l,onPointerMove:j(e.onPointerMove,v=>{v.pointerType!=="touch"&&!f.current&&!s.isPointerInTransitRef.current&&(r.onTriggerEnter(),f.current=!0)}),onPointerLeave:j(e.onPointerLeave,()=>{r.onTriggerLeave(),f.current=!1}),onPointerDown:j(e.onPointerDown,()=>{r.open&&r.onClose(),c.current=!0,document.addEventListener("pointerup",d,{once:!0})}),onFocus:j(e.onFocus,()=>{c.current||r.onOpen()}),onBlur:j(e.onBlur,r.onClose),onClick:j(e.onClick,r.onClose)})})});jr.displayName=Ye;var $r="TooltipPortal",[us,Wr]=Me($r,{forceMount:void 0}),ce="TooltipContent",gn=u.forwardRef((e,t)=>{const n=Wr(ce,e.__scopeTooltip),{forceMount:o=n.forceMount,side:r="top",...s}=e,i=Ie(ce,e.__scopeTooltip);return b.jsx(Ge,{present:o||i.open,children:i.disableHoverableContent?b.jsx(wn,{side:r,...s,ref:t}):b.jsx(Hr,{side:r,...s,ref:t})})}),Hr=u.forwardRef((e,t)=>{const n=Ie(ce,e.__scopeTooltip),o=mn(ce,e.__scopeTooltip),r=u.useRef(null),s=V(t,r),[i,a]=u.useState(null),{trigger:l,onClose:c}=n,f=r.current,{onPointerInTransitChange:d}=o,v=u.useCallback(()=>{a(null),d(!1)},[d]),p=u.useCallback((g,h)=>{const m=g.currentTarget,y={x:g.clientX,y:g.clientY},w=Kr(y,m.getBoundingClientRect()),x=Yr(y,w),E=Xr(h.getBoundingClientRect()),T=Gr([...x,...E]);a(T),d(!0)},[d]);return u.useEffect(()=>()=>v(),[v]),u.useEffect(()=>{if(l&&f){const g=m=>p(m,f),h=m=>p(m,l);return l.addEventListener("pointerleave",g),f.addEventListener("pointerleave",h),()=>{l.removeEventListener("pointerleave",g),f.removeEventListener("pointerleave",h)}}},[l,f,p,v]),u.useEffect(()=>{if(i){const g=h=>{const m=h.target,y={x:h.clientX,y:h.clientY},w=l?.contains(m)||f?.contains(m),x=!qr(y,i);w?v():x&&(v(),c())};return document.addEventListener("pointermove",g),()=>document.removeEventListener("pointermove",g)}},[l,f,i,c,v]),b.jsx(wn,{...e,ref:s})}),[Br,Vr]=Me(vn,{isInside:!1}),zr=Fn("TooltipContent"),wn=u.forwardRef((e,t)=>{const{__scopeTooltip:n,children:o,"aria-label":r,onEscapeKeyDown:s,onPointerDownOutside:i,...a}=e,l=Ie(ce,n),c=at(n),{onClose:f}=l;return u.useEffect(()=>(document.addEventListener(bt,f),()=>document.removeEventListener(bt,f)),[f]),u.useEffect(()=>{if(l.trigger){const d=v=>{v.target?.contains(l.trigger)&&f()};return window.addEventListener("scroll",d,{capture:!0}),()=>window.removeEventListener("scroll",d,{capture:!0})}},[l.trigger,f]),b.jsx(qe,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:s,onPointerDownOutside:i,onFocusOutside:d=>d.preventDefault(),onDismiss:f,children:b.jsxs(Mr,{"data-state":l.stateAttribute,...c,...a,ref:t,style:{...a.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[b.jsx(zr,{children:o}),b.jsx(Br,{scope:n,isInside:!0,children:b.jsx(co,{id:l.contentId,role:"tooltip",children:r||o})})]})})});gn.displayName=ce;var yn="TooltipArrow",Ur=u.forwardRef((e,t)=>{const{__scopeTooltip:n,...o}=e,r=at(n);return Vr(yn,n).isInside?null:b.jsx(Ir,{...r,...o,ref:t})});Ur.displayName=yn;function Kr(e,t){const n=Math.abs(t.top-e.y),o=Math.abs(t.bottom-e.y),r=Math.abs(t.right-e.x),s=Math.abs(t.left-e.x);switch(Math.min(n,o,r,s)){case s:return"left";case r:return"right";case n:return"top";case o:return"bottom";default:throw new Error("unreachable")}}function Yr(e,t,n=5){const o=[];switch(t){case"top":o.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":o.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":o.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":o.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return o}function Xr(e){const{top:t,right:n,bottom:o,left:r}=e;return[{x:r,y:t},{x:n,y:t},{x:n,y:o},{x:r,y:o}]}function qr(e,t){const{x:n,y:o}=e;let r=!1;for(let s=0,i=t.length-1;s<t.length;i=s++){const a=t[s],l=t[i],c=a.x,f=a.y,d=l.x,v=l.y;f>o!=v>o&&n<(d-c)*(o-f)/(v-f)+c&&(r=!r)}return r}function Gr(e){const t=e.slice();return t.sort((n,o)=>n.x<o.x?-1:n.x>o.x?1:n.y<o.y?-1:n.y>o.y?1:0),Zr(t)}function Zr(e){if(e.length<=1)return e.slice();const t=[];for(let o=0;o<e.length;o++){const r=e[o];for(;t.length>=2;){const s=t[t.length-1],i=t[t.length-2];if((s.x-i.x)*(r.y-i.y)>=(s.y-i.y)*(r.x-i.x))t.pop();else break}t.push(r)}t.pop();const n=[];for(let o=e.length-1;o>=0;o--){const r=e[o];for(;n.length>=2;){const s=n[n.length-1],i=n[n.length-2];if((s.x-i.x)*(r.y-i.y)>=(s.y-i.y)*(r.x-i.x))n.pop();else break}n.push(r)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var fs=hn,ds=gn;export{ss as A,is as C,rs as D,es as P,ns as R,os as T,ts as V,ds as a,fs as b,b as j};
