const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/Index-C2OLKmMC.js","assets/js/ui-vendor-WY5xTeZk.js","assets/js/react-vendor-o6ozJo2K.js","assets/js/utils-vendor--BulIq_u.js","assets/js/query-vendor--kaXPEoe.js","assets/js/router-vendor-CClBJTgV.js","assets/js/NotFound-Cpc_r-E2.js"])))=>i.map(i=>d[i]);
import{j as h,V as qe,R as Je,A as Ge,C as Xe,T as Qe,D as Ze,P as St,a as et,b as Tt}from"./ui-vendor-WY5xTeZk.js";import{a as Ct,r as w,o as n,v as Nt}from"./react-vendor-o6ozJo2K.js";import{t as kt,c as Pt,a as jt}from"./utils-vendor--BulIq_u.js";import{Q as It,a as Mt}from"./query-vendor--kaXPEoe.js";import{B as Rt,R as $t,a as Fe}from"./router-vendor-CClBJTgV.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&r(l)}).observe(document,{childList:!0,subtree:!0});function a(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(s){if(s.ep)return;s.ep=!0;const i=a(s);fetch(s.href,i)}})();var tt,_e=Ct;tt=_e.createRoot,_e.hydrateRoot;const At="modulepreload",Ot=function(t){return"/"+t},Ye={},at=function(e,a,r){let s=Promise.resolve();if(a&&a.length>0){document.getElementsByTagName("link");const l=document.querySelector("meta[property=csp-nonce]"),u=l?.nonce||l?.getAttribute("nonce");s=Promise.allSettled(a.map(d=>{if(d=Ot(d),d in Ye)return;Ye[d]=!0;const m=d.endsWith(".css"),p=m?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${d}"]${p}`))return;const T=document.createElement("link");if(T.rel=m?"stylesheet":At,m||(T.as="script"),T.crossOrigin="",T.href=d,u&&T.setAttribute("nonce",u),document.head.appendChild(T),m)return new Promise((P,o)=>{T.addEventListener("load",P),T.addEventListener("error",()=>o(new Error(`Unable to preload CSS for ${d}`)))})}))}function i(l){const u=new Event("vite:preloadError",{cancelable:!0});if(u.payload=l,window.dispatchEvent(u),!u.defaultPrevented)throw l}return s.then(l=>{for(const u of l||[])u.status==="rejected"&&i(u.reason);return e().catch(i)})},Lt=1,Bt=1e6;let ke=0;function zt(){return ke=(ke+1)%Number.MAX_SAFE_INTEGER,ke.toString()}const Pe=new Map,Ve=t=>{if(Pe.has(t))return;const e=setTimeout(()=>{Pe.delete(t),ne({type:"REMOVE_TOAST",toastId:t})},Bt);Pe.set(t,e)},Dt=(t,e)=>{switch(e.type){case"ADD_TOAST":return{...t,toasts:[e.toast,...t.toasts].slice(0,Lt)};case"UPDATE_TOAST":return{...t,toasts:t.toasts.map(a=>a.id===e.toast.id?{...a,...e.toast}:a)};case"DISMISS_TOAST":{const{toastId:a}=e;return a?Ve(a):t.toasts.forEach(r=>{Ve(r.id)}),{...t,toasts:t.toasts.map(r=>r.id===a||a===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return e.toastId===void 0?{...t,toasts:[]}:{...t,toasts:t.toasts.filter(a=>a.id!==e.toastId)}}},ve=[];let ye={toasts:[]};function ne(t){ye=Dt(ye,t),ve.forEach(e=>{e(ye)})}function Ft({...t}){const e=zt(),a=s=>ne({type:"UPDATE_TOAST",toast:{...s,id:e}}),r=()=>ne({type:"DISMISS_TOAST",toastId:e});return ne({type:"ADD_TOAST",toast:{...t,id:e,open:!0,onOpenChange:s=>{s||r()}}}),{id:e,dismiss:r,update:a}}function _t(){const[t,e]=w.useState(ye);return w.useEffect(()=>(ve.push(e),()=>{const a=ve.indexOf(e);a>-1&&ve.splice(a,1)}),[t]),{...t,toast:Ft,dismiss:a=>ne({type:"DISMISS_TOAST",toastId:a})}}/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yt=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),rt=(...t)=>t.filter((e,a,r)=>!!e&&e.trim()!==""&&r.indexOf(e)===a).join(" ").trim();/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Vt={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ht=w.forwardRef(({color:t="currentColor",size:e=24,strokeWidth:a=2,absoluteStrokeWidth:r,className:s="",children:i,iconNode:l,...u},d)=>w.createElement("svg",{ref:d,...Vt,width:e,height:e,stroke:t,strokeWidth:r?Number(a)*24/Number(e):a,className:rt("lucide",s),...u},[...l.map(([m,p])=>w.createElement(m,p)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wt=(t,e)=>{const a=w.forwardRef(({className:r,...s},i)=>w.createElement(Ht,{ref:i,iconNode:e,className:rt(`lucide-${Yt(t)}`,r),...s}));return a.displayName=`${t}`,a};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kt=Wt("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function q(...t){return kt(Pt(t))}const Ut=St,ot=w.forwardRef(({className:t,...e},a)=>h.jsx(qe,{ref:a,className:q("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",t),...e}));ot.displayName=qe.displayName;const qt=jt("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),st=w.forwardRef(({className:t,variant:e,...a},r)=>h.jsx(Je,{ref:r,className:q(qt({variant:e}),t),...a}));st.displayName=Je.displayName;const Jt=w.forwardRef(({className:t,...e},a)=>h.jsx(Ge,{ref:a,className:q("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",t),...e}));Jt.displayName=Ge.displayName;const nt=w.forwardRef(({className:t,...e},a)=>h.jsx(Xe,{ref:a,className:q("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",t),"toast-close":"",...e,children:h.jsx(Kt,{className:"h-4 w-4"})}));nt.displayName=Xe.displayName;const it=w.forwardRef(({className:t,...e},a)=>h.jsx(Qe,{ref:a,className:q("text-sm font-semibold",t),...e}));it.displayName=Qe.displayName;const lt=w.forwardRef(({className:t,...e},a)=>h.jsx(Ze,{ref:a,className:q("text-sm opacity-90",t),...e}));lt.displayName=Ze.displayName;function Gt(){const{toasts:t}=_t();return h.jsxs(Ut,{children:[t.map(function({id:e,title:a,description:r,action:s,...i}){return h.jsxs(st,{...i,children:[h.jsxs("div",{className:"grid gap-1",children:[a&&h.jsx(it,{children:a}),r&&h.jsx(lt,{children:r})]}),s,h.jsx(nt,{})]},e)}),h.jsx(ot,{})]})}var He=["light","dark"],Xt="(prefers-color-scheme: dark)",Qt=w.createContext(void 0),Zt={setTheme:t=>{},themes:[]},ea=()=>{var t;return(t=w.useContext(Qt))!=null?t:Zt};w.memo(({forcedTheme:t,storageKey:e,attribute:a,enableSystem:r,enableColorScheme:s,defaultTheme:i,value:l,attrs:u,nonce:d})=>{let m=i==="system",p=a==="class"?`var d=document.documentElement,c=d.classList;${`c.remove(${u.map(R=>`'${R}'`).join(",")})`};`:`var d=document.documentElement,n='${a}',s='setAttribute';`,T=s?He.includes(i)&&i?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${i}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",P=(R,y=!1,I=!0)=>{let _=l?l[R]:R,z=y?R+"|| ''":`'${_}'`,C="";return s&&I&&!y&&He.includes(R)&&(C+=`d.style.colorScheme = '${R}';`),a==="class"?y||_?C+=`c.add(${z})`:C+="null":_&&(C+=`d[s](n,${z})`),C},o=t?`!function(){${p}${P(t)}}()`:r?`!function(){try{${p}var e=localStorage.getItem('${e}');if('system'===e||(!e&&${m})){var t='${Xt}',m=window.matchMedia(t);if(m.media!==t||m.matches){${P("dark")}}else{${P("light")}}}else if(e){${l?`var x=${JSON.stringify(l)};`:""}${P(l?"x[e]":"e",!0)}}${m?"":"else{"+P(i,!1,!1)+"}"}${T}}catch(e){}}()`:`!function(){try{${p}var e=localStorage.getItem('${e}');if(e){${l?`var x=${JSON.stringify(l)};`:""}${P(l?"x[e]":"e",!0)}}else{${P(i,!1,!1)};}${T}}catch(t){}}();`;return w.createElement("script",{nonce:d,dangerouslySetInnerHTML:{__html:o}})});var ta=t=>{switch(t){case"success":return oa;case"info":return na;case"warning":return sa;case"error":return ia;default:return null}},aa=Array(12).fill(0),ra=({visible:t,className:e})=>n.createElement("div",{className:["sonner-loading-wrapper",e].filter(Boolean).join(" "),"data-visible":t},n.createElement("div",{className:"sonner-spinner"},aa.map((a,r)=>n.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${r}`})))),oa=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),sa=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),na=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),ia=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),la=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},n.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),n.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),ca=()=>{let[t,e]=n.useState(document.hidden);return n.useEffect(()=>{let a=()=>{e(document.hidden)};return document.addEventListener("visibilitychange",a),()=>window.removeEventListener("visibilitychange",a)},[]),t},je=1,da=class{constructor(){this.subscribe=t=>(this.subscribers.push(t),()=>{let e=this.subscribers.indexOf(t);this.subscribers.splice(e,1)}),this.publish=t=>{this.subscribers.forEach(e=>e(t))},this.addToast=t=>{this.publish(t),this.toasts=[...this.toasts,t]},this.create=t=>{var e;let{message:a,...r}=t,s=typeof t?.id=="number"||((e=t.id)==null?void 0:e.length)>0?t.id:je++,i=this.toasts.find(u=>u.id===s),l=t.dismissible===void 0?!0:t.dismissible;return this.dismissedToasts.has(s)&&this.dismissedToasts.delete(s),i?this.toasts=this.toasts.map(u=>u.id===s?(this.publish({...u,...t,id:s,title:a}),{...u,...t,id:s,dismissible:l,title:a}):u):this.addToast({title:a,...r,dismissible:l,id:s}),s},this.dismiss=t=>(this.dismissedToasts.add(t),t||this.toasts.forEach(e=>{this.subscribers.forEach(a=>a({id:e.id,dismiss:!0}))}),this.subscribers.forEach(e=>e({id:t,dismiss:!0})),t),this.message=(t,e)=>this.create({...e,message:t}),this.error=(t,e)=>this.create({...e,message:t,type:"error"}),this.success=(t,e)=>this.create({...e,type:"success",message:t}),this.info=(t,e)=>this.create({...e,type:"info",message:t}),this.warning=(t,e)=>this.create({...e,type:"warning",message:t}),this.loading=(t,e)=>this.create({...e,type:"loading",message:t}),this.promise=(t,e)=>{if(!e)return;let a;e.loading!==void 0&&(a=this.create({...e,promise:t,type:"loading",message:e.loading,description:typeof e.description!="function"?e.description:void 0}));let r=t instanceof Promise?t:t(),s=a!==void 0,i,l=r.then(async d=>{if(i=["resolve",d],n.isValidElement(d))s=!1,this.create({id:a,type:"default",message:d});else if(ma(d)&&!d.ok){s=!1;let m=typeof e.error=="function"?await e.error(`HTTP error! status: ${d.status}`):e.error,p=typeof e.description=="function"?await e.description(`HTTP error! status: ${d.status}`):e.description;this.create({id:a,type:"error",message:m,description:p})}else if(e.success!==void 0){s=!1;let m=typeof e.success=="function"?await e.success(d):e.success,p=typeof e.description=="function"?await e.description(d):e.description;this.create({id:a,type:"success",message:m,description:p})}}).catch(async d=>{if(i=["reject",d],e.error!==void 0){s=!1;let m=typeof e.error=="function"?await e.error(d):e.error,p=typeof e.description=="function"?await e.description(d):e.description;this.create({id:a,type:"error",message:m,description:p})}}).finally(()=>{var d;s&&(this.dismiss(a),a=void 0),(d=e.finally)==null||d.call(e)}),u=()=>new Promise((d,m)=>l.then(()=>i[0]==="reject"?m(i[1]):d(i[1])).catch(m));return typeof a!="string"&&typeof a!="number"?{unwrap:u}:Object.assign(a,{unwrap:u})},this.custom=(t,e)=>{let a=e?.id||je++;return this.create({jsx:t(a),id:a,...e}),a},this.getActiveToasts=()=>this.toasts.filter(t=>!this.dismissedToasts.has(t.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}},k=new da,ua=(t,e)=>{let a=e?.id||je++;return k.addToast({title:t,...e,id:a}),a},ma=t=>t&&typeof t=="object"&&"ok"in t&&typeof t.ok=="boolean"&&"status"in t&&typeof t.status=="number",fa=ua,ha=()=>k.toasts,pa=()=>k.getActiveToasts();Object.assign(fa,{success:k.success,info:k.info,warning:k.warning,error:k.error,custom:k.custom,message:k.message,promise:k.promise,dismiss:k.dismiss,loading:k.loading},{getHistory:ha,getToasts:pa});function ga(t,{insertAt:e}={}){if(typeof document>"u")return;let a=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",e==="top"&&a.firstChild?a.insertBefore(r,a.firstChild):a.appendChild(r),r.styleSheet?r.styleSheet.cssText=t:r.appendChild(document.createTextNode(t))}ga(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted="true"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted="true"]){transform:none}}:where([data-sonner-toaster][data-x-position="right"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position="left"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function pe(t){return t.label!==void 0}var va=3,ya="32px",ba="16px",We=4e3,wa=356,xa=14,Ea=20,Sa=200;function B(...t){return t.filter(Boolean).join(" ")}function Ta(t){let[e,a]=t.split("-"),r=[];return e&&r.push(e),a&&r.push(a),r}var Ca=t=>{var e,a,r,s,i,l,u,d,m,p,T;let{invert:P,toast:o,unstyled:R,interacting:y,setHeights:I,visibleToasts:_,heights:z,index:C,toasts:be,expanded:ee,removeToast:$,defaultRichColors:te,closeButton:ie,style:le,cancelButtonStyle:we,actionButtonStyle:ce,className:D="",descriptionClassName:de="",duration:J,position:ue,gap:W,loadingIcon:F,expandByDefault:me,classNames:f,icons:j,closeButtonAriaLabel:xe="Close toast",pauseWhenPageIsHidden:v}=t,[b,E]=n.useState(null),[N,G]=n.useState(null),[S,Ee]=n.useState(!1),[ae,fe]=n.useState(!1),[re,Se]=n.useState(!1),[Re,dt]=n.useState(!1),[ut,$e]=n.useState(!1),[mt,Te]=n.useState(0),[ft,Ae]=n.useState(0),oe=n.useRef(o.duration||J||We),Oe=n.useRef(null),K=n.useRef(null),ht=C===0,pt=C+1<=_,M=o.type,X=o.dismissible!==!1,gt=o.className||"",vt=o.descriptionClassName||"",he=n.useMemo(()=>z.findIndex(c=>c.toastId===o.id)||0,[z,o.id]),yt=n.useMemo(()=>{var c;return(c=o.closeButton)!=null?c:ie},[o.closeButton,ie]),Le=n.useMemo(()=>o.duration||J||We,[o.duration,J]),Ce=n.useRef(0),Q=n.useRef(0),Be=n.useRef(0),Z=n.useRef(null),[bt,wt]=ue.split("-"),ze=n.useMemo(()=>z.reduce((c,g,x)=>x>=he?c:c+g.height,0),[z,he]),De=ca(),xt=o.invert||P,Ne=M==="loading";Q.current=n.useMemo(()=>he*W+ze,[he,ze]),n.useEffect(()=>{oe.current=Le},[Le]),n.useEffect(()=>{Ee(!0)},[]),n.useEffect(()=>{let c=K.current;if(c){let g=c.getBoundingClientRect().height;return Ae(g),I(x=>[{toastId:o.id,height:g,position:o.position},...x]),()=>I(x=>x.filter(A=>A.toastId!==o.id))}},[I,o.id]),n.useLayoutEffect(()=>{if(!S)return;let c=K.current,g=c.style.height;c.style.height="auto";let x=c.getBoundingClientRect().height;c.style.height=g,Ae(x),I(A=>A.find(O=>O.toastId===o.id)?A.map(O=>O.toastId===o.id?{...O,height:x}:O):[{toastId:o.id,height:x,position:o.position},...A])},[S,o.title,o.description,I,o.id]);let Y=n.useCallback(()=>{fe(!0),Te(Q.current),I(c=>c.filter(g=>g.toastId!==o.id)),setTimeout(()=>{$(o)},Sa)},[o,$,I,Q]);n.useEffect(()=>{if(o.promise&&M==="loading"||o.duration===1/0||o.type==="loading")return;let c;return ee||y||v&&De?(()=>{if(Be.current<Ce.current){let g=new Date().getTime()-Ce.current;oe.current=oe.current-g}Be.current=new Date().getTime()})():oe.current!==1/0&&(Ce.current=new Date().getTime(),c=setTimeout(()=>{var g;(g=o.onAutoClose)==null||g.call(o,o),Y()},oe.current)),()=>clearTimeout(c)},[ee,y,o,M,v,De,Y]),n.useEffect(()=>{o.delete&&Y()},[Y,o.delete]);function Et(){var c,g,x;return j!=null&&j.loading?n.createElement("div",{className:B(f?.loader,(c=o?.classNames)==null?void 0:c.loader,"sonner-loader"),"data-visible":M==="loading"},j.loading):F?n.createElement("div",{className:B(f?.loader,(g=o?.classNames)==null?void 0:g.loader,"sonner-loader"),"data-visible":M==="loading"},F):n.createElement(ra,{className:B(f?.loader,(x=o?.classNames)==null?void 0:x.loader),visible:M==="loading"})}return n.createElement("li",{tabIndex:0,ref:K,className:B(D,gt,f?.toast,(e=o?.classNames)==null?void 0:e.toast,f?.default,f?.[M],(a=o?.classNames)==null?void 0:a[M]),"data-sonner-toast":"","data-rich-colors":(r=o.richColors)!=null?r:te,"data-styled":!(o.jsx||o.unstyled||R),"data-mounted":S,"data-promise":!!o.promise,"data-swiped":ut,"data-removed":ae,"data-visible":pt,"data-y-position":bt,"data-x-position":wt,"data-index":C,"data-front":ht,"data-swiping":re,"data-dismissible":X,"data-type":M,"data-invert":xt,"data-swipe-out":Re,"data-swipe-direction":N,"data-expanded":!!(ee||me&&S),style:{"--index":C,"--toasts-before":C,"--z-index":be.length-C,"--offset":`${ae?mt:Q.current}px`,"--initial-height":me?"auto":`${ft}px`,...le,...o.style},onDragEnd:()=>{Se(!1),E(null),Z.current=null},onPointerDown:c=>{Ne||!X||(Oe.current=new Date,Te(Q.current),c.target.setPointerCapture(c.pointerId),c.target.tagName!=="BUTTON"&&(Se(!0),Z.current={x:c.clientX,y:c.clientY}))},onPointerUp:()=>{var c,g,x,A;if(Re||!X)return;Z.current=null;let O=Number(((c=K.current)==null?void 0:c.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),V=Number(((g=K.current)==null?void 0:g.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),U=new Date().getTime()-((x=Oe.current)==null?void 0:x.getTime()),L=b==="x"?O:V,H=Math.abs(L)/U;if(Math.abs(L)>=Ea||H>.11){Te(Q.current),(A=o.onDismiss)==null||A.call(o,o),G(b==="x"?O>0?"right":"left":V>0?"down":"up"),Y(),dt(!0),$e(!1);return}Se(!1),E(null)},onPointerMove:c=>{var g,x,A,O;if(!Z.current||!X||((g=window.getSelection())==null?void 0:g.toString().length)>0)return;let V=c.clientY-Z.current.y,U=c.clientX-Z.current.x,L=(x=t.swipeDirections)!=null?x:Ta(ue);!b&&(Math.abs(U)>1||Math.abs(V)>1)&&E(Math.abs(U)>Math.abs(V)?"x":"y");let H={x:0,y:0};b==="y"?(L.includes("top")||L.includes("bottom"))&&(L.includes("top")&&V<0||L.includes("bottom")&&V>0)&&(H.y=V):b==="x"&&(L.includes("left")||L.includes("right"))&&(L.includes("left")&&U<0||L.includes("right")&&U>0)&&(H.x=U),(Math.abs(H.x)>0||Math.abs(H.y)>0)&&$e(!0),(A=K.current)==null||A.style.setProperty("--swipe-amount-x",`${H.x}px`),(O=K.current)==null||O.style.setProperty("--swipe-amount-y",`${H.y}px`)}},yt&&!o.jsx?n.createElement("button",{"aria-label":xe,"data-disabled":Ne,"data-close-button":!0,onClick:Ne||!X?()=>{}:()=>{var c;Y(),(c=o.onDismiss)==null||c.call(o,o)},className:B(f?.closeButton,(s=o?.classNames)==null?void 0:s.closeButton)},(i=j?.close)!=null?i:la):null,o.jsx||w.isValidElement(o.title)?o.jsx?o.jsx:typeof o.title=="function"?o.title():o.title:n.createElement(n.Fragment,null,M||o.icon||o.promise?n.createElement("div",{"data-icon":"",className:B(f?.icon,(l=o?.classNames)==null?void 0:l.icon)},o.promise||o.type==="loading"&&!o.icon?o.icon||Et():null,o.type!=="loading"?o.icon||j?.[M]||ta(M):null):null,n.createElement("div",{"data-content":"",className:B(f?.content,(u=o?.classNames)==null?void 0:u.content)},n.createElement("div",{"data-title":"",className:B(f?.title,(d=o?.classNames)==null?void 0:d.title)},typeof o.title=="function"?o.title():o.title),o.description?n.createElement("div",{"data-description":"",className:B(de,vt,f?.description,(m=o?.classNames)==null?void 0:m.description)},typeof o.description=="function"?o.description():o.description):null),w.isValidElement(o.cancel)?o.cancel:o.cancel&&pe(o.cancel)?n.createElement("button",{"data-button":!0,"data-cancel":!0,style:o.cancelButtonStyle||we,onClick:c=>{var g,x;pe(o.cancel)&&X&&((x=(g=o.cancel).onClick)==null||x.call(g,c),Y())},className:B(f?.cancelButton,(p=o?.classNames)==null?void 0:p.cancelButton)},o.cancel.label):null,w.isValidElement(o.action)?o.action:o.action&&pe(o.action)?n.createElement("button",{"data-button":!0,"data-action":!0,style:o.actionButtonStyle||ce,onClick:c=>{var g,x;pe(o.action)&&((x=(g=o.action).onClick)==null||x.call(g,c),!c.defaultPrevented&&Y())},className:B(f?.actionButton,(T=o?.classNames)==null?void 0:T.actionButton)},o.action.label):null))};function Ke(){if(typeof window>"u"||typeof document>"u")return"ltr";let t=document.documentElement.getAttribute("dir");return t==="auto"||!t?window.getComputedStyle(document.documentElement).direction:t}function Na(t,e){let a={};return[t,e].forEach((r,s)=>{let i=s===1,l=i?"--mobile-offset":"--offset",u=i?ba:ya;function d(m){["top","right","bottom","left"].forEach(p=>{a[`${l}-${p}`]=typeof m=="number"?`${m}px`:m})}typeof r=="number"||typeof r=="string"?d(r):typeof r=="object"?["top","right","bottom","left"].forEach(m=>{r[m]===void 0?a[`${l}-${m}`]=u:a[`${l}-${m}`]=typeof r[m]=="number"?`${r[m]}px`:r[m]}):d(u)}),a}var ka=w.forwardRef(function(t,e){let{invert:a,position:r="bottom-right",hotkey:s=["altKey","KeyT"],expand:i,closeButton:l,className:u,offset:d,mobileOffset:m,theme:p="light",richColors:T,duration:P,style:o,visibleToasts:R=va,toastOptions:y,dir:I=Ke(),gap:_=xa,loadingIcon:z,icons:C,containerAriaLabel:be="Notifications",pauseWhenPageIsHidden:ee}=t,[$,te]=n.useState([]),ie=n.useMemo(()=>Array.from(new Set([r].concat($.filter(v=>v.position).map(v=>v.position)))),[$,r]),[le,we]=n.useState([]),[ce,D]=n.useState(!1),[de,J]=n.useState(!1),[ue,W]=n.useState(p!=="system"?p:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),F=n.useRef(null),me=s.join("+").replace(/Key/g,"").replace(/Digit/g,""),f=n.useRef(null),j=n.useRef(!1),xe=n.useCallback(v=>{te(b=>{var E;return(E=b.find(N=>N.id===v.id))!=null&&E.delete||k.dismiss(v.id),b.filter(({id:N})=>N!==v.id)})},[]);return n.useEffect(()=>k.subscribe(v=>{if(v.dismiss){te(b=>b.map(E=>E.id===v.id?{...E,delete:!0}:E));return}setTimeout(()=>{Nt.flushSync(()=>{te(b=>{let E=b.findIndex(N=>N.id===v.id);return E!==-1?[...b.slice(0,E),{...b[E],...v},...b.slice(E+1)]:[v,...b]})})})}),[]),n.useEffect(()=>{if(p!=="system"){W(p);return}if(p==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?W("dark"):W("light")),typeof window>"u")return;let v=window.matchMedia("(prefers-color-scheme: dark)");try{v.addEventListener("change",({matches:b})=>{W(b?"dark":"light")})}catch{v.addListener(({matches:E})=>{try{W(E?"dark":"light")}catch(N){console.error(N)}})}},[p]),n.useEffect(()=>{$.length<=1&&D(!1)},[$]),n.useEffect(()=>{let v=b=>{var E,N;s.every(G=>b[G]||b.code===G)&&(D(!0),(E=F.current)==null||E.focus()),b.code==="Escape"&&(document.activeElement===F.current||(N=F.current)!=null&&N.contains(document.activeElement))&&D(!1)};return document.addEventListener("keydown",v),()=>document.removeEventListener("keydown",v)},[s]),n.useEffect(()=>{if(F.current)return()=>{f.current&&(f.current.focus({preventScroll:!0}),f.current=null,j.current=!1)}},[F.current]),n.createElement("section",{ref:e,"aria-label":`${be} ${me}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},ie.map((v,b)=>{var E;let[N,G]=v.split("-");return $.length?n.createElement("ol",{key:v,dir:I==="auto"?Ke():I,tabIndex:-1,ref:F,className:u,"data-sonner-toaster":!0,"data-theme":ue,"data-y-position":N,"data-lifted":ce&&$.length>1&&!i,"data-x-position":G,style:{"--front-toast-height":`${((E=le[0])==null?void 0:E.height)||0}px`,"--width":`${wa}px`,"--gap":`${_}px`,...o,...Na(d,m)},onBlur:S=>{j.current&&!S.currentTarget.contains(S.relatedTarget)&&(j.current=!1,f.current&&(f.current.focus({preventScroll:!0}),f.current=null))},onFocus:S=>{S.target instanceof HTMLElement&&S.target.dataset.dismissible==="false"||j.current||(j.current=!0,f.current=S.relatedTarget)},onMouseEnter:()=>D(!0),onMouseMove:()=>D(!0),onMouseLeave:()=>{de||D(!1)},onDragEnd:()=>D(!1),onPointerDown:S=>{S.target instanceof HTMLElement&&S.target.dataset.dismissible==="false"||J(!0)},onPointerUp:()=>J(!1)},$.filter(S=>!S.position&&b===0||S.position===v).map((S,Ee)=>{var ae,fe;return n.createElement(Ca,{key:S.id,icons:C,index:Ee,toast:S,defaultRichColors:T,duration:(ae=y?.duration)!=null?ae:P,className:y?.className,descriptionClassName:y?.descriptionClassName,invert:a,visibleToasts:R,closeButton:(fe=y?.closeButton)!=null?fe:l,interacting:de,position:v,style:y?.style,unstyled:y?.unstyled,classNames:y?.classNames,cancelButtonStyle:y?.cancelButtonStyle,actionButtonStyle:y?.actionButtonStyle,removeToast:xe,toasts:$.filter(re=>re.position==S.position),heights:le.filter(re=>re.position==S.position),setHeights:we,expandByDefault:i,gap:_,loadingIcon:z,expanded:ce,pauseWhenPageIsHidden:ee,swipeDirections:t.swipeDirections})})):null}))});const Pa=({...t})=>{const{theme:e="system"}=ea();return h.jsx(ka,{theme:e,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...t})},ja=Tt,Ia=w.forwardRef(({className:t,sideOffset:e=4,...a},r)=>h.jsx(et,{ref:r,sideOffset:e,className:q("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...a}));Ia.displayName=et.displayName;const Ma=w.lazy(()=>at(()=>import("./Index-C2OLKmMC.js"),__vite__mapDeps([0,1,2,3,4,5]))),Ra=w.lazy(()=>at(()=>import("./NotFound-Cpc_r-E2.js"),__vite__mapDeps([6,1,2,5]))),$a=()=>h.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50",children:h.jsxs("div",{className:"text-center space-y-4",children:[h.jsx("div",{className:"w-12 h-12 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto"}),h.jsx("p",{className:"text-slate-600 font-medium",children:"Lädt..."})]})}),Aa=new It,Oa=()=>h.jsx(Mt,{client:Aa,children:h.jsxs(ja,{children:[h.jsx(Gt,{}),h.jsx(Pa,{}),h.jsx(Rt,{children:h.jsx(w.Suspense,{fallback:h.jsx($a,{}),children:h.jsxs($t,{children:[h.jsx(Fe,{path:"/",element:h.jsx(Ma,{})}),h.jsx(Fe,{path:"*",element:h.jsx(Ra,{})})]})})})]})}),Ie={IMAGES:{name:"suz-images",version:"1.0.0",maxAge:24*60*60*1e3,maxEntries:100},API:{name:"suz-api",version:"1.0.0",maxAge:5*60*1e3,maxEntries:50},STATIC:{name:"suz-static",version:"1.0.0",maxAge:7*24*60*60*1e3,maxEntries:200}};class Me{config;storageKey;constructor(e){this.config=e,this.storageKey=`${e.name}-${e.version}`}async set(e,a){try{const r=Date.now(),s={data:a,timestamp:r,expires:r+this.config.maxAge},i=await this.getCache();i[e]=s,await this.enforceMaxEntries(i),localStorage.setItem(this.storageKey,JSON.stringify(i))}catch(r){console.warn("[Cache] Failed to set cache entry:",r)}}async get(e){try{const a=await this.getCache(),r=a[e];return r?Date.now()>r.expires?(delete a[e],localStorage.setItem(this.storageKey,JSON.stringify(a)),null):r.data:null}catch(a){return console.warn("[Cache] Failed to get cache entry:",a),null}}async has(e){return await this.get(e)!==null}async delete(e){try{const a=await this.getCache();delete a[e],localStorage.setItem(this.storageKey,JSON.stringify(a))}catch(a){console.warn("[Cache] Failed to delete cache entry:",a)}}async clear(){try{localStorage.removeItem(this.storageKey)}catch(e){console.warn("[Cache] Failed to clear cache:",e)}}async getStats(){try{const e=await this.getCache(),a=Object.values(e),r=a.map(s=>s.timestamp);return{size:JSON.stringify(e).length,entries:a.length,oldestEntry:r.length>0?Math.min(...r):null,newestEntry:r.length>0?Math.max(...r):null}}catch(e){return console.warn("[Cache] Failed to get cache stats:",e),{size:0,entries:0,oldestEntry:null,newestEntry:null}}}async cleanup(){try{const e=await this.getCache(),a=Date.now();let r=0;for(const[s,i]of Object.entries(e))a>i.expires&&(delete e[s],r++);return r>0&&localStorage.setItem(this.storageKey,JSON.stringify(e)),r}catch(e){return console.warn("[Cache] Failed to cleanup cache:",e),0}}async getCache(){try{const e=localStorage.getItem(this.storageKey);return e?JSON.parse(e):{}}catch(e){return console.warn("[Cache] Failed to parse cache, resetting:",e),{}}}async enforceMaxEntries(e){const a=Object.entries(e);if(a.length<=this.config.maxEntries)return;a.sort(([,s],[,i])=>s.timestamp-i.timestamp);const r=a.length-this.config.maxEntries;for(let s=0;s<r;s++)delete e[a[s][0]]}}const ct=new Me(Ie.IMAGES),La=new Me(Ie.API),Ba=new Me(Ie.STATIC);async function za(){const e=["/assets/logos/logo.png","/assets/images/hero-bg.jpg"].map(async a=>{try{const r=await fetch(a);if(r.ok){const s=await r.blob();await ct.set(a,URL.createObjectURL(s))}}catch(r){console.warn(`[Cache] Failed to preload ${a}:`,r)}});await Promise.allSettled(e)}async function Da(){await Promise.all([ct.cleanup(),La.cleanup(),Ba.cleanup()]),await za(),console.log("[Cache] Cache system initialized")}const Fa={LCP:{good:2500,poor:4e3},FID:{good:100,poor:300},CLS:{good:.1,poor:.25},FCP:{good:1800,poor:3e3},TTFB:{good:800,poor:1800}};function se(t,e){const a=Fa[t];return e<=a.good?"good":e<=a.poor?"needs-improvement":"poor"}class _a{constructor(e){this.onMetric=e,this.initializeObservers()}metrics={};observers=[];initializeObservers(){this.observeLCP(),this.observeFID(),this.observeCLS(),this.observeFCP(),this.observeTTFB()}observeLCP(){if("PerformanceObserver"in window)try{const e=new PerformanceObserver(a=>{const r=a.getEntries(),s=r[r.length-1];if(s){const i=s.renderTime||s.loadTime||s.startTime,l={name:"LCP",value:i,rating:se("LCP",i),delta:i-(this.metrics.lcp?.value||0),id:`lcp-${Date.now()}`};this.metrics.lcp=l,this.onMetric?.(l)}});e.observe({type:"largest-contentful-paint",buffered:!0}),this.observers.push(e)}catch(e){console.warn("[Performance] LCP observer failed:",e)}}observeFID(){if("PerformanceObserver"in window)try{const e=new PerformanceObserver(a=>{a.getEntries().forEach(s=>{const i=s.processingStart-s.startTime,l={name:"FID",value:i,rating:se("FID",i),delta:i-(this.metrics.fid?.value||0),id:`fid-${Date.now()}`};this.metrics.fid=l,this.onMetric?.(l)})});e.observe({type:"first-input",buffered:!0}),this.observers.push(e)}catch(e){console.warn("[Performance] FID observer failed:",e)}}observeCLS(){if("PerformanceObserver"in window)try{let e=0,a=0,r=[];const s=new PerformanceObserver(i=>{i.getEntries().forEach(u=>{if(!u.hadRecentInput){const d=r[0],m=r[r.length-1];if(a&&u.startTime-m.startTime<1e3&&u.startTime-d.startTime<5e3?(a+=u.value,r.push(u)):(a=u.value,r=[u]),a>e){e=a;const p={name:"CLS",value:e,rating:se("CLS",e),delta:e-(this.metrics.cls?.value||0),id:`cls-${Date.now()}`};this.metrics.cls=p,this.onMetric?.(p)}}})});s.observe({type:"layout-shift",buffered:!0}),this.observers.push(s)}catch(e){console.warn("[Performance] CLS observer failed:",e)}}observeFCP(){if("PerformanceObserver"in window)try{const e=new PerformanceObserver(a=>{a.getEntries().forEach(s=>{if(s.name==="first-contentful-paint"){const i=s.startTime,l={name:"FCP",value:i,rating:se("FCP",i),delta:i-(this.metrics.fcp?.value||0),id:`fcp-${Date.now()}`};this.metrics.fcp=l,this.onMetric?.(l)}})});e.observe({type:"paint",buffered:!0}),this.observers.push(e)}catch(e){console.warn("[Performance] FCP observer failed:",e)}}observeTTFB(){try{const e=performance.getEntriesByType("navigation")[0];if(e){const a=e.responseStart-e.requestStart,r={name:"TTFB",value:a,rating:se("TTFB",a),delta:a-(this.metrics.ttfb?.value||0),id:`ttfb-${Date.now()}`};this.metrics.ttfb=r,this.onMetric?.(r)}}catch(e){console.warn("[Performance] TTFB measurement failed:",e)}}getMetrics(){return{...this.metrics}}getPerformanceScore(){const e=Object.values(this.metrics);if(e.length===0)return 0;const a=e.map(r=>{switch(r.rating){case"good":return 100;case"needs-improvement":return 75;case"poor":return 50;default:return 0}});return Math.round(a.reduce((r,s)=>r+s,0)/a.length)}disconnect(){this.observers.forEach(e=>e.disconnect()),this.observers=[]}}class ge{static preloadCriticalResources(){[{href:"/assets/logos/logo.png",as:"image"},{href:"/assets/images/hero-bg.jpg",as:"image"}].forEach(({href:a,as:r})=>{const s=document.createElement("link");s.rel="preload",s.href=a,s.as=r,document.head.appendChild(s)})}static optimizeImages(){document.querySelectorAll("img[data-optimize]").forEach(a=>{const r=a;r.loading||(r.loading="lazy"),r.decoding="async",(!r.width||!r.height)&&(r.style.aspectRatio="16/9")})}static reduceLayoutShifts(){document.querySelectorAll("img:not([width]):not([height])").forEach(r=>{const s=r;s.style.aspectRatio="16/9",s.style.width="100%",s.style.height="auto"}),document.querySelectorAll("[data-dynamic]").forEach(r=>{const s=r;s.style.minHeight||(s.style.minHeight="200px")})}static optimizeFonts(){const e=document.createElement("style");e.textContent=`
      @font-face {
        font-family: -apple-system;
        font-display: swap;
      }
    `,document.head.appendChild(e)}}let Ue=null;function Ya(){Ue||(Ue=new _a(t=>{console.log(`[Performance] ${t.name}:`,{value:Math.round(t.value),rating:t.rating,delta:Math.round(t.delta)})}),ge.preloadCriticalResources(),ge.optimizeImages(),ge.reduceLayoutShifts(),ge.optimizeFonts(),console.log("[Performance] Performance monitoring initialized"))}"serviceWorker"in navigator&&window.addEventListener("load",()=>{navigator.serviceWorker.register("/sw.js").then(t=>{console.log("SW registered: ",t),t.addEventListener("updatefound",()=>{const e=t.installing;e&&e.addEventListener("statechange",()=>{e.state==="installed"&&navigator.serviceWorker.controller&&confirm("Neue Version verfügbar. Seite neu laden?")&&(e.postMessage({type:"SKIP_WAITING"}),window.location.reload())})})}).catch(t=>{console.log("SW registration failed: ",t)})});Da().catch(console.error);Ya();tt(document.getElementById("root")).render(h.jsx(Oa,{}));export{at as _,Wt as c};
