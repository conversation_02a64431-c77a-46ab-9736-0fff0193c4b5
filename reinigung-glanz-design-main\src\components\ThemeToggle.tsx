import { useTheme } from '@/hooks/use-theme';
import { Moon, Sun } from 'lucide-react';

export function ThemeToggle() {
  const { theme, toggleTheme } = useTheme();

  return (
    <button
      onClick={toggleTheme}
      className="
        fixed top-8 right-8 z-50 
        glass-morphism-premium p-4 rounded-3xl 
        border border-white/30 shadow-xl backdrop-blur-xl
        transition-all duration-300 hover:scale-110
        focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
        group
      "
      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
    >
      <div className="relative w-6 h-6">
        <Sun 
          className={`
            absolute inset-0 w-6 h-6 text-yellow-500 
            transition-all duration-300 transform
            ${theme === 'light' ? 'rotate-0 scale-100 opacity-100' : 'rotate-90 scale-0 opacity-0'}
          `}
        />
        <Moon 
          className={`
            absolute inset-0 w-6 h-6 text-blue-400 
            transition-all duration-300 transform
            ${theme === 'dark' ? 'rotate-0 scale-100 opacity-100' : '-rotate-90 scale-0 opacity-0'}
          `}
        />
      </div>
      
      {/* Tooltip */}
      <div className="
        absolute -bottom-12 left-1/2 transform -translate-x-1/2
        bg-gray-900 text-white text-sm px-3 py-1 rounded-lg
        opacity-0 group-hover:opacity-100 transition-opacity duration-200
        pointer-events-none whitespace-nowrap
        dark:bg-white dark:text-gray-900
      ">
        {theme === 'light' ? 'Switch to dark mode' : 'Switch to light mode'}
      </div>
    </button>
  );
}
