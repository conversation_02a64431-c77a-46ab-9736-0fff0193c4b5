
const Team = () => {
  const teamMembers = [
    { name: "<PERSON>", role: "Teamleitung", image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=face" },
    { name: "<PERSON>", role: "Fachkraft", image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=300&h=300&fit=crop&crop=face" },
    { name: "<PERSON><PERSON>", role: "Fachkraft", image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face" },
    { name: "<PERSON>", role: "Qualitätskontrolle", image: "https://images.unsplash.com/photo-1531123897727-8f129e1688ce?w=300&h=300&fit=crop&crop=face" }
  ];

  return (
    <section
      id="team"
      className="px-4"
      style={{ padding: `var(--section-padding-lg) var(--space-4)` }}
    >
      <div className="max-w-7xl mx-auto">
        <div className="text-center animate-fade-in" style={{ marginBottom: 'var(--space-20)' }}>
          <h2 className="suz-section-title text-slate-800 mb-8">
            Unser <span className="gradient-text">Team</span>
          </h2>
          <p className="suz-text-heading-xl text-slate-600 max-w-3xl mx-auto">
            Professionelle Reinigungskräfte mit Leidenschaft für Sauberkeit
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-10">
          {teamMembers.map((member, index) => (
            <article
              key={index}
              className="suz-card-glass team-card rounded-3xl border border-white/30 text-center group shadow-2xl animate-fade-in"
              style={{
                animationDelay: `${index * 0.15}s`,
                padding: 'var(--component-padding-lg)'
              }}
            >
              <div className="mb-6">
                <img
                  src={member.image}
                  alt={`${member.name} - ${member.role} bei SUZ Reinigung`}
                  className="w-28 h-28 rounded-full mx-auto object-cover border-4 border-white/50 shadow-lg image-optimized group-hover:scale-105 transition-transform duration-300"
                  loading="lazy"
                  decoding="async"
                />
              </div>
              <h3 className="suz-text-heading-lg font-semibold text-slate-800 mb-3">
                {member.name}
              </h3>
              <p className="suz-text-body-lg text-blue-600 font-medium">
                {member.role}
              </p>
            </article>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Team;
