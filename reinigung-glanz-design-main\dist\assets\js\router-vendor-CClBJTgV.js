import{r as c,R as Y}from"./react-vendor-o6ozJo2K.js";/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function b(){return b=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},b.apply(this,arguments)}var w;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(w||(w={}));const T="popstate";function Z(e){e===void 0&&(e={});function t(n,o){let{pathname:l,search:a,hash:s}=n.location;return O("",{pathname:l,search:a,hash:s},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function r(n,o){return typeof o=="string"?o:D(o)}return ee(t,r,null,e)}function x(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function A(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function H(){return Math.random().toString(36).substr(2,8)}function _(e,t){return{usr:e.state,key:e.key,idx:t}}function O(e,t,r,n){return r===void 0&&(r=null),b({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?I(t):t,{state:r,key:t&&t.key||n||H()})}function D(e){let{pathname:t="/",search:r="",hash:n=""}=e;return r&&r!=="?"&&(t+=r.charAt(0)==="?"?r:"?"+r),n&&n!=="#"&&(t+=n.charAt(0)==="#"?n:"#"+n),t}function I(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}function ee(e,t,r,n){n===void 0&&(n={});let{window:o=document.defaultView,v5Compat:l=!1}=n,a=o.history,s=w.Pop,i=null,h=f();h==null&&(h=0,a.replaceState(b({},a.state,{idx:h}),""));function f(){return(a.state||{idx:null}).idx}function u(){s=w.Pop;let d=f(),y=d==null?null:d-h;h=d,i&&i({action:s,location:p.location,delta:y})}function m(d,y){s=w.Push;let E=O(p.location,d,y);h=f()+1;let C=_(E,h),R=p.createHref(E);try{a.pushState(C,"",R)}catch(L){if(L instanceof DOMException&&L.name==="DataCloneError")throw L;o.location.assign(R)}l&&i&&i({action:s,location:p.location,delta:1})}function g(d,y){s=w.Replace;let E=O(p.location,d,y);h=f();let C=_(E,h),R=p.createHref(E);a.replaceState(C,"",R),l&&i&&i({action:s,location:p.location,delta:0})}function v(d){let y=o.location.origin!=="null"?o.location.origin:o.location.href,E=typeof d=="string"?d:D(d);return E=E.replace(/ $/,"%20"),x(y,"No window.location.(origin|href) available to create URL for href: "+E),new URL(E,y)}let p={get action(){return s},get location(){return e(o,a)},listen(d){if(i)throw new Error("A history only accepts one active listener");return o.addEventListener(T,u),i=d,()=>{o.removeEventListener(T,u),i=null}},createHref(d){return t(o,d)},createURL:v,encodeLocation(d){let y=v(d);return{pathname:y.pathname,search:y.search,hash:y.hash}},push:m,replace:g,go(d){return a.go(d)}};return p}var j;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(j||(j={}));function te(e,t,r){return r===void 0&&(r="/"),re(e,t,r)}function re(e,t,r,n){let o=typeof t=="string"?I(t):t,l=G(o.pathname||"/",r);if(l==null)return null;let a=z(e);ne(a);let s=null;for(let i=0;s==null&&i<a.length;++i){let h=me(l);s=he(a[i],h)}return s}function z(e,t,r,n){t===void 0&&(t=[]),r===void 0&&(r=[]),n===void 0&&(n="");let o=(l,a,s)=>{let i={relativePath:s===void 0?l.path||"":s,caseSensitive:l.caseSensitive===!0,childrenIndex:a,route:l};i.relativePath.startsWith("/")&&(x(i.relativePath.startsWith(n),'Absolute route path "'+i.relativePath+'" nested under path '+('"'+n+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),i.relativePath=i.relativePath.slice(n.length));let h=P([n,i.relativePath]),f=r.concat(i);l.children&&l.children.length>0&&(x(l.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+h+'".')),z(l.children,t,f,h)),!(l.path==null&&!l.index)&&t.push({path:h,score:ce(h,l.index),routesMeta:f})};return e.forEach((l,a)=>{var s;if(l.path===""||!((s=l.path)!=null&&s.includes("?")))o(l,a);else for(let i of q(l.path))o(l,a,i)}),t}function q(e){let t=e.split("/");if(t.length===0)return[];let[r,...n]=t,o=r.endsWith("?"),l=r.replace(/\?$/,"");if(n.length===0)return o?[l,""]:[l];let a=q(n.join("/")),s=[];return s.push(...a.map(i=>i===""?l:[l,i].join("/"))),o&&s.push(...a),s.map(i=>e.startsWith("/")&&i===""?"/":i)}function ne(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:de(t.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}const oe=/^:[\w-]+$/,le=3,ae=2,ie=1,se=10,ue=-2,N=e=>e==="*";function ce(e,t){let r=e.split("/"),n=r.length;return r.some(N)&&(n+=ue),t&&(n+=ae),r.filter(o=>!N(o)).reduce((o,l)=>o+(oe.test(l)?le:l===""?ie:se),n)}function de(e,t){return e.length===t.length&&e.slice(0,-1).every((n,o)=>n===t[o])?e[e.length-1]-t[t.length-1]:0}function he(e,t,r){let{routesMeta:n}=e,o={},l="/",a=[];for(let s=0;s<n.length;++s){let i=n[s],h=s===n.length-1,f=l==="/"?t:t.slice(l.length)||"/",u=fe({path:i.relativePath,caseSensitive:i.caseSensitive,end:h},f),m=i.route;if(!u)return null;Object.assign(o,u.params),a.push({params:o,pathname:P([l,u.pathname]),pathnameBase:ve(P([l,u.pathnameBase])),route:m}),u.pathnameBase!=="/"&&(l=P([l,u.pathnameBase]))}return a}function fe(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=pe(e.path,e.caseSensitive,e.end),o=t.match(r);if(!o)return null;let l=o[0],a=l.replace(/(.)\/+$/,"$1"),s=o.slice(1);return{params:n.reduce((h,f,u)=>{let{paramName:m,isOptional:g}=f;if(m==="*"){let p=s[u]||"";a=l.slice(0,l.length-p.length).replace(/(.)\/+$/,"$1")}const v=s[u];return g&&!v?h[m]=void 0:h[m]=(v||"").replace(/%2F/g,"/"),h},{}),pathname:l,pathnameBase:a,pattern:e}}function pe(e,t,r){t===void 0&&(t=!1),r===void 0&&(r=!0),A(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let n=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(a,s,i)=>(n.push({paramName:s,isOptional:i!=null}),i?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),n]}function me(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return A(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function G(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&n!=="/"?null:e.slice(r)||"/"}const P=e=>e.join("/").replace(/\/\/+/g,"/"),ve=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/");function ge(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const J=["post","put","patch","delete"];new Set(J);const ye=["get",...J];new Set(ye);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function B(){return B=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},B.apply(this,arguments)}const xe=c.createContext(null),Ee=c.createContext(null),K=c.createContext(null),U=c.createContext(null),S=c.createContext({outlet:null,matches:[],isDataRoute:!1}),Q=c.createContext(null);function F(){return c.useContext(U)!=null}function Ce(){return F()||x(!1),c.useContext(U).location}function we(e,t){return Pe(e,t)}function Pe(e,t,r,n){F()||x(!1);let{navigator:o}=c.useContext(K),{matches:l}=c.useContext(S),a=l[l.length-1],s=a?a.params:{};a&&a.pathname;let i=a?a.pathnameBase:"/";a&&a.route;let h=Ce(),f;if(t){var u;let d=typeof t=="string"?I(t):t;i==="/"||(u=d.pathname)!=null&&u.startsWith(i)||x(!1),f=d}else f=h;let m=f.pathname||"/",g=m;if(i!=="/"){let d=i.replace(/^\//,"").split("/");g="/"+m.replace(/^\//,"").split("/").slice(d.length).join("/")}let v=te(e,{pathname:g}),p=Ue(v&&v.map(d=>Object.assign({},d,{params:Object.assign({},s,d.params),pathname:P([i,o.encodeLocation?o.encodeLocation(d.pathname).pathname:d.pathname]),pathnameBase:d.pathnameBase==="/"?i:P([i,o.encodeLocation?o.encodeLocation(d.pathnameBase).pathname:d.pathnameBase])})),l,r,n);return t&&p?c.createElement(U.Provider,{value:{location:B({pathname:"/",search:"",hash:"",state:null,key:"default"},f),navigationType:w.Pop}},p):p}function Re(){let e=$e(),t=ge(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return c.createElement(c.Fragment,null,c.createElement("h2",null,"Unexpected Application Error!"),c.createElement("h3",{style:{fontStyle:"italic"}},t),r?c.createElement("pre",{style:o},r):null,null)}const be=c.createElement(Re,null);class Be extends c.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,r){return r.location!==t.location||r.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:r.error,location:r.location,revalidation:t.revalidation||r.revalidation}}componentDidCatch(t,r){console.error("React Router caught the following error during render",t,r)}render(){return this.state.error!==void 0?c.createElement(S.Provider,{value:this.props.routeContext},c.createElement(Q.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Ie(e){let{routeContext:t,match:r,children:n}=e,o=c.useContext(xe);return o&&o.static&&o.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=r.route.id),c.createElement(S.Provider,{value:t},n)}function Ue(e,t,r,n){var o;if(t===void 0&&(t=[]),r===void 0&&(r=null),n===void 0&&(n=null),e==null){var l;if(!r)return null;if(r.errors)e=r.matches;else if((l=n)!=null&&l.v7_partialHydration&&t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let a=e,s=(o=r)==null?void 0:o.errors;if(s!=null){let f=a.findIndex(u=>u.route.id&&s?.[u.route.id]!==void 0);f>=0||x(!1),a=a.slice(0,Math.min(a.length,f+1))}let i=!1,h=-1;if(r&&n&&n.v7_partialHydration)for(let f=0;f<a.length;f++){let u=a[f];if((u.route.HydrateFallback||u.route.hydrateFallbackElement)&&(h=f),u.route.id){let{loaderData:m,errors:g}=r,v=u.route.loader&&m[u.route.id]===void 0&&(!g||g[u.route.id]===void 0);if(u.route.lazy||v){i=!0,h>=0?a=a.slice(0,h+1):a=[a[0]];break}}}return a.reduceRight((f,u,m)=>{let g,v=!1,p=null,d=null;r&&(g=s&&u.route.id?s[u.route.id]:void 0,p=u.route.errorElement||be,i&&(h<0&&m===0?(Fe("route-fallback"),v=!0,d=null):h===m&&(v=!0,d=u.route.hydrateFallbackElement||null)));let y=t.concat(a.slice(0,m+1)),E=()=>{let C;return g?C=p:v?C=d:u.route.Component?C=c.createElement(u.route.Component,null):u.route.element?C=u.route.element:C=f,c.createElement(Ie,{match:u,routeContext:{outlet:f,matches:y,isDataRoute:r!=null},children:C})};return r&&(u.route.ErrorBoundary||u.route.errorElement||m===0)?c.createElement(Be,{location:r.location,revalidation:r.revalidation,component:p,error:g,children:E(),routeContext:{outlet:null,matches:y,isDataRoute:!0}}):E()},null)}var X=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(X||{});function Se(e){let t=c.useContext(Ee);return t||x(!1),t}function Le(e){let t=c.useContext(S);return t||x(!1),t}function Oe(e){let t=Le(),r=t.matches[t.matches.length-1];return r.route.id||x(!1),r.route.id}function $e(){var e;let t=c.useContext(Q),r=Se(X.UseRouteError),n=Oe();return t!==void 0?t:(e=r.errors)==null?void 0:e[n]}const M={};function Fe(e,t,r){M[e]||(M[e]=!0)}function Te(e,t){e?.v7_startTransition,e?.v7_relativeSplatPath}function _e(e){x(!1)}function je(e){let{basename:t="/",children:r=null,location:n,navigationType:o=w.Pop,navigator:l,static:a=!1,future:s}=e;F()&&x(!1);let i=t.replace(/^\/*/,"/"),h=c.useMemo(()=>({basename:i,navigator:l,static:a,future:B({v7_relativeSplatPath:!1},s)}),[i,s,l,a]);typeof n=="string"&&(n=I(n));let{pathname:f="/",search:u="",hash:m="",state:g=null,key:v="default"}=n,p=c.useMemo(()=>{let d=G(f,i);return d==null?null:{location:{pathname:d,search:u,hash:m,state:g,key:v},navigationType:o}},[i,f,u,m,g,v,o]);return p==null?null:c.createElement(K.Provider,{value:h},c.createElement(U.Provider,{children:r,value:p}))}function We(e){let{children:t,location:r}=e;return we($(t),r)}new Promise(()=>{});function $(e,t){t===void 0&&(t=[]);let r=[];return c.Children.forEach(e,(n,o)=>{if(!c.isValidElement(n))return;let l=[...t,o];if(n.type===c.Fragment){r.push.apply(r,$(n.props.children,l));return}n.type!==_e&&x(!1),!n.props.index||!n.props.children||x(!1);let a={id:n.props.id||l.join("-"),caseSensitive:n.props.caseSensitive,element:n.props.element,Component:n.props.Component,index:n.props.index,path:n.props.path,loader:n.props.loader,action:n.props.action,errorElement:n.props.errorElement,ErrorBoundary:n.props.ErrorBoundary,hasErrorBoundary:n.props.ErrorBoundary!=null||n.props.errorElement!=null,shouldRevalidate:n.props.shouldRevalidate,handle:n.props.handle,lazy:n.props.lazy};n.props.children&&(a.children=$(n.props.children,l)),r.push(a)}),r}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const Ne="6";try{window.__reactRouterVersion=Ne}catch{}const Me="startTransition",V=Y[Me];function ke(e){let{basename:t,children:r,future:n,window:o}=e,l=c.useRef();l.current==null&&(l.current=Z({window:o,v5Compat:!0}));let a=l.current,[s,i]=c.useState({action:a.action,location:a.location}),{v7_startTransition:h}=n||{},f=c.useCallback(u=>{h&&V?V(()=>i(u)):i(u)},[i,h]);return c.useLayoutEffect(()=>a.listen(f),[a,f]),c.useEffect(()=>Te(n),[n]),c.createElement(je,{basename:t,children:r,location:s.location,navigationType:s.action,navigator:a,future:n})}var W;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(W||(W={}));var k;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(k||(k={}));export{ke as B,We as R,_e as a,Ce as u};
