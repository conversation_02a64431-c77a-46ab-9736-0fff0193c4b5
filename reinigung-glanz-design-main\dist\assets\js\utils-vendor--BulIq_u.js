function ee(e){var t,r,o="";if(typeof e=="string"||typeof e=="number")o+=e;else if(typeof e=="object")if(Array.isArray(e)){var l=e.length;for(t=0;t<l;t++)e[t]&&(r=ee(e[t]))&&(o&&(o+=" "),o+=r)}else for(r in e)e[r]&&(o&&(o+=" "),o+=r);return o}function le(){for(var e,t,r=0,o="",l=arguments.length;r<l;r++)(e=arguments[r])&&(t=ee(e))&&(o&&(o+=" "),o+=t);return o}const H=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,Q=le,Oe=(e,t)=>r=>{var o;if(t?.variants==null)return Q(e,r?.class,r?.className);const{variants:l,defaultVariants:n}=t,a=Object.keys(l).map(c=>{const g=r?.[c],h=n?.[c];if(g===null)return null;const m=H(g)||H(h);return l[c][m]}),s=r&&Object.entries(r).reduce((c,g)=>{let[h,m]=g;return m===void 0||(c[h]=m),c},{}),b=t==null||(o=t.compoundVariants)===null||o===void 0?void 0:o.reduce((c,g)=>{let{class:h,className:m,...w}=g;return Object.entries(w).every(x=>{let[f,d]=x;return Array.isArray(d)?d.includes({...n,...s}[f]):{...n,...s}[f]===d})?[...c,h,m]:c},[]);return Q(e,a,b,r?.class,r?.className)},U="-",ie=e=>{const t=ce(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:a=>{const s=a.split(U);return s[0]===""&&s.length!==1&&s.shift(),re(s,t)||ae(a)},getConflictingClassGroupIds:(a,s)=>{const b=r[a]||[];return s&&o[a]?[...b,...o[a]]:b}}},re=(e,t)=>{if(e.length===0)return t.classGroupId;const r=e[0],o=t.nextPart.get(r),l=o?re(e.slice(1),o):void 0;if(l)return l;if(t.validators.length===0)return;const n=e.join(U);return t.validators.find(({validator:a})=>a(n))?.classGroupId},Y=/^\[(.+)\]$/,ae=e=>{if(Y.test(e)){const t=Y.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},ce=e=>{const{theme:t,prefix:r}=e,o={nextPart:new Map,validators:[]};return ue(Object.entries(e.classGroups),r).forEach(([n,a])=>{$(a,o,n,t)}),o},$=(e,t,r,o)=>{e.forEach(l=>{if(typeof l=="string"){const n=l===""?t:D(t,l);n.classGroupId=r;return}if(typeof l=="function"){if(de(l)){$(l(o),t,r,o);return}t.validators.push({validator:l,classGroupId:r});return}Object.entries(l).forEach(([n,a])=>{$(a,D(t,n),r,o)})})},D=(e,t)=>{let r=e;return t.split(U).forEach(o=>{r.nextPart.has(o)||r.nextPart.set(o,{nextPart:new Map,validators:[]}),r=r.nextPart.get(o)}),r},de=e=>e.isThemeGetter,ue=(e,t)=>t?e.map(([r,o])=>{const l=o.map(n=>typeof n=="string"?t+n:typeof n=="object"?Object.fromEntries(Object.entries(n).map(([a,s])=>[t+a,s])):n);return[r,l]}):e,pe=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,r=new Map,o=new Map;const l=(n,a)=>{r.set(n,a),t++,t>e&&(t=0,o=r,r=new Map)};return{get(n){let a=r.get(n);if(a!==void 0)return a;if((a=o.get(n))!==void 0)return l(n,a),a},set(n,a){r.has(n)?r.set(n,a):l(n,a)}}},te="!",be=e=>{const{separator:t,experimentalParseClassName:r}=e,o=t.length===1,l=t[0],n=t.length,a=s=>{const b=[];let c=0,g=0,h;for(let d=0;d<s.length;d++){let y=s[d];if(c===0){if(y===l&&(o||s.slice(d,d+n)===t)){b.push(s.slice(g,d)),g=d+n;continue}if(y==="/"){h=d;continue}}y==="["?c++:y==="]"&&c--}const m=b.length===0?s:s.substring(g),w=m.startsWith(te),x=w?m.substring(1):m,f=h&&h>g?h-g:void 0;return{modifiers:b,hasImportantModifier:w,baseClassName:x,maybePostfixModifierPosition:f}};return r?s=>r({className:s,parseClassName:a}):a},ge=e=>{if(e.length<=1)return e;const t=[];let r=[];return e.forEach(o=>{o[0]==="["?(t.push(...r.sort(),o),r=[]):r.push(o)}),t.push(...r.sort()),t},fe=e=>({cache:pe(e.cacheSize),parseClassName:be(e),...ie(e)}),me=/\s+/,he=(e,t)=>{const{parseClassName:r,getClassGroupId:o,getConflictingClassGroupIds:l}=t,n=[],a=e.trim().split(me);let s="";for(let b=a.length-1;b>=0;b-=1){const c=a[b],{modifiers:g,hasImportantModifier:h,baseClassName:m,maybePostfixModifierPosition:w}=r(c);let x=!!w,f=o(x?m.substring(0,w):m);if(!f){if(!x){s=c+(s.length>0?" "+s:s);continue}if(f=o(m),!f){s=c+(s.length>0?" "+s:s);continue}x=!1}const d=ge(g).join(":"),y=h?d+te:d,v=y+f;if(n.includes(v))continue;n.push(v);const P=l(f,x);for(let S=0;S<P.length;++S){const j=P[S];n.push(y+j)}s=c+(s.length>0?" "+s:s)}return s};function ye(){let e=0,t,r,o="";for(;e<arguments.length;)(t=arguments[e++])&&(r=oe(t))&&(o&&(o+=" "),o+=r);return o}const oe=e=>{if(typeof e=="string")return e;let t,r="";for(let o=0;o<e.length;o++)e[o]&&(t=oe(e[o]))&&(r&&(r+=" "),r+=t);return r};function xe(e,...t){let r,o,l,n=a;function a(b){const c=t.reduce((g,h)=>h(g),e());return r=fe(c),o=r.cache.get,l=r.cache.set,n=s,s(b)}function s(b){const c=o(b);if(c)return c;const g=he(b,r);return l(b,g),g}return function(){return n(ye.apply(null,arguments))}}const u=e=>{const t=r=>r[e]||[];return t.isThemeGetter=!0,t},ne=/^\[(?:([a-z-]+):)?(.+)\]$/i,ve=/^\d+\/\d+$/,we=new Set(["px","full","screen"]),Ce=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,ke=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,ze=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Ae=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Se=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,k=e=>M(e)||we.has(e)||ve.test(e),z=e=>G(e,"length",Ee),M=e=>!!e&&!Number.isNaN(Number(e)),_=e=>G(e,"number",M),N=e=>!!e&&Number.isInteger(Number(e)),Me=e=>e.endsWith("%")&&M(e.slice(0,-1)),i=e=>ne.test(e),A=e=>Ce.test(e),Ge=new Set(["length","size","percentage"]),Pe=e=>G(e,Ge,se),Re=e=>G(e,"position",se),Ne=new Set(["image","url"]),Ie=e=>G(e,Ne,Ve),je=e=>G(e,"",Te),I=()=>!0,G=(e,t,r)=>{const o=ne.exec(e);return o?o[1]?typeof t=="string"?o[1]===t:t.has(o[1]):r(o[2]):!1},Ee=e=>ke.test(e)&&!ze.test(e),se=()=>!1,Te=e=>Ae.test(e),Ve=e=>Se.test(e),Le=()=>{const e=u("colors"),t=u("spacing"),r=u("blur"),o=u("brightness"),l=u("borderColor"),n=u("borderRadius"),a=u("borderSpacing"),s=u("borderWidth"),b=u("contrast"),c=u("grayscale"),g=u("hueRotate"),h=u("invert"),m=u("gap"),w=u("gradientColorStops"),x=u("gradientColorStopPositions"),f=u("inset"),d=u("margin"),y=u("opacity"),v=u("padding"),P=u("saturate"),S=u("scale"),j=u("sepia"),B=u("skew"),F=u("space"),q=u("translate"),V=()=>["auto","contain","none"],L=()=>["auto","hidden","clip","visible","scroll"],O=()=>["auto",i,t],p=()=>[i,t],J=()=>["",k,z],E=()=>["auto",M,i],K=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],T=()=>["solid","dashed","dotted","double","none"],X=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],W=()=>["start","end","center","between","around","evenly","stretch"],R=()=>["","0",i],Z=()=>["auto","avoid","all","avoid-page","page","left","right","column"],C=()=>[M,i];return{cacheSize:500,separator:":",theme:{colors:[I],spacing:[k,z],blur:["none","",A,i],brightness:C(),borderColor:[e],borderRadius:["none","","full",A,i],borderSpacing:p(),borderWidth:J(),contrast:C(),grayscale:R(),hueRotate:C(),invert:R(),gap:p(),gradientColorStops:[e],gradientColorStopPositions:[Me,z],inset:O(),margin:O(),opacity:C(),padding:p(),saturate:C(),scale:C(),sepia:R(),skew:C(),space:p(),translate:p()},classGroups:{aspect:[{aspect:["auto","square","video",i]}],container:["container"],columns:[{columns:[A]}],"break-after":[{"break-after":Z()}],"break-before":[{"break-before":Z()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...K(),i]}],overflow:[{overflow:L()}],"overflow-x":[{"overflow-x":L()}],"overflow-y":[{"overflow-y":L()}],overscroll:[{overscroll:V()}],"overscroll-x":[{"overscroll-x":V()}],"overscroll-y":[{"overscroll-y":V()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[f]}],"inset-x":[{"inset-x":[f]}],"inset-y":[{"inset-y":[f]}],start:[{start:[f]}],end:[{end:[f]}],top:[{top:[f]}],right:[{right:[f]}],bottom:[{bottom:[f]}],left:[{left:[f]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",N,i]}],basis:[{basis:O()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",i]}],grow:[{grow:R()}],shrink:[{shrink:R()}],order:[{order:["first","last","none",N,i]}],"grid-cols":[{"grid-cols":[I]}],"col-start-end":[{col:["auto",{span:["full",N,i]},i]}],"col-start":[{"col-start":E()}],"col-end":[{"col-end":E()}],"grid-rows":[{"grid-rows":[I]}],"row-start-end":[{row:["auto",{span:[N,i]},i]}],"row-start":[{"row-start":E()}],"row-end":[{"row-end":E()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",i]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",i]}],gap:[{gap:[m]}],"gap-x":[{"gap-x":[m]}],"gap-y":[{"gap-y":[m]}],"justify-content":[{justify:["normal",...W()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...W(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...W(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[v]}],px:[{px:[v]}],py:[{py:[v]}],ps:[{ps:[v]}],pe:[{pe:[v]}],pt:[{pt:[v]}],pr:[{pr:[v]}],pb:[{pb:[v]}],pl:[{pl:[v]}],m:[{m:[d]}],mx:[{mx:[d]}],my:[{my:[d]}],ms:[{ms:[d]}],me:[{me:[d]}],mt:[{mt:[d]}],mr:[{mr:[d]}],mb:[{mb:[d]}],ml:[{ml:[d]}],"space-x":[{"space-x":[F]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[F]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",i,t]}],"min-w":[{"min-w":[i,t,"min","max","fit"]}],"max-w":[{"max-w":[i,t,"none","full","min","max","fit","prose",{screen:[A]},A]}],h:[{h:[i,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[i,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[i,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[i,t,"auto","min","max","fit"]}],"font-size":[{text:["base",A,z]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",_]}],"font-family":[{font:[I]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",i]}],"line-clamp":[{"line-clamp":["none",M,_]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",k,i]}],"list-image":[{"list-image":["none",i]}],"list-style-type":[{list:["none","disc","decimal",i]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[y]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[y]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...T(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",k,z]}],"underline-offset":[{"underline-offset":["auto",k,i]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:p()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",i]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",i]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[y]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...K(),Re]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Pe]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Ie]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[x]}],"gradient-via-pos":[{via:[x]}],"gradient-to-pos":[{to:[x]}],"gradient-from":[{from:[w]}],"gradient-via":[{via:[w]}],"gradient-to":[{to:[w]}],rounded:[{rounded:[n]}],"rounded-s":[{"rounded-s":[n]}],"rounded-e":[{"rounded-e":[n]}],"rounded-t":[{"rounded-t":[n]}],"rounded-r":[{"rounded-r":[n]}],"rounded-b":[{"rounded-b":[n]}],"rounded-l":[{"rounded-l":[n]}],"rounded-ss":[{"rounded-ss":[n]}],"rounded-se":[{"rounded-se":[n]}],"rounded-ee":[{"rounded-ee":[n]}],"rounded-es":[{"rounded-es":[n]}],"rounded-tl":[{"rounded-tl":[n]}],"rounded-tr":[{"rounded-tr":[n]}],"rounded-br":[{"rounded-br":[n]}],"rounded-bl":[{"rounded-bl":[n]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[y]}],"border-style":[{border:[...T(),"hidden"]}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[y]}],"divide-style":[{divide:T()}],"border-color":[{border:[l]}],"border-color-x":[{"border-x":[l]}],"border-color-y":[{"border-y":[l]}],"border-color-s":[{"border-s":[l]}],"border-color-e":[{"border-e":[l]}],"border-color-t":[{"border-t":[l]}],"border-color-r":[{"border-r":[l]}],"border-color-b":[{"border-b":[l]}],"border-color-l":[{"border-l":[l]}],"divide-color":[{divide:[l]}],"outline-style":[{outline:["",...T()]}],"outline-offset":[{"outline-offset":[k,i]}],"outline-w":[{outline:[k,z]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:J()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[y]}],"ring-offset-w":[{"ring-offset":[k,z]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",A,je]}],"shadow-color":[{shadow:[I]}],opacity:[{opacity:[y]}],"mix-blend":[{"mix-blend":[...X(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":X()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[o]}],contrast:[{contrast:[b]}],"drop-shadow":[{"drop-shadow":["","none",A,i]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[g]}],invert:[{invert:[h]}],saturate:[{saturate:[P]}],sepia:[{sepia:[j]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[o]}],"backdrop-contrast":[{"backdrop-contrast":[b]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[g]}],"backdrop-invert":[{"backdrop-invert":[h]}],"backdrop-opacity":[{"backdrop-opacity":[y]}],"backdrop-saturate":[{"backdrop-saturate":[P]}],"backdrop-sepia":[{"backdrop-sepia":[j]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[a]}],"border-spacing-x":[{"border-spacing-x":[a]}],"border-spacing-y":[{"border-spacing-y":[a]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",i]}],duration:[{duration:C()}],ease:[{ease:["linear","in","out","in-out",i]}],delay:[{delay:C()}],animate:[{animate:["none","spin","ping","pulse","bounce",i]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[S]}],"scale-x":[{"scale-x":[S]}],"scale-y":[{"scale-y":[S]}],rotate:[{rotate:[N,i]}],"translate-x":[{"translate-x":[q]}],"translate-y":[{"translate-y":[q]}],"skew-x":[{"skew-x":[B]}],"skew-y":[{"skew-y":[B]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",i]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",i]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":p()}],"scroll-mx":[{"scroll-mx":p()}],"scroll-my":[{"scroll-my":p()}],"scroll-ms":[{"scroll-ms":p()}],"scroll-me":[{"scroll-me":p()}],"scroll-mt":[{"scroll-mt":p()}],"scroll-mr":[{"scroll-mr":p()}],"scroll-mb":[{"scroll-mb":p()}],"scroll-ml":[{"scroll-ml":p()}],"scroll-p":[{"scroll-p":p()}],"scroll-px":[{"scroll-px":p()}],"scroll-py":[{"scroll-py":p()}],"scroll-ps":[{"scroll-ps":p()}],"scroll-pe":[{"scroll-pe":p()}],"scroll-pt":[{"scroll-pt":p()}],"scroll-pr":[{"scroll-pr":p()}],"scroll-pb":[{"scroll-pb":p()}],"scroll-pl":[{"scroll-pl":p()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",i]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[k,z,_]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},We=xe(Le);export{Oe as a,le as c,We as t};
