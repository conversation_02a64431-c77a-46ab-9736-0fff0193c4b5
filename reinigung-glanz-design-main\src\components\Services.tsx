
const Services = () => {
  const services = [
    {
      title: "Hotelzimmerreinigung",
      description: "Tiefenreinigung und tägliche Pflege für höchste Hygienestandards in Hotelzimmern.",
      icon: "🏨"
    },
    {
      title: "Teppichreinigung", 
      description: "Tiefenreinigung für Teppiche und Polster. Wir entfernen Flecken, Gerüche und Allergene für ein frisches und hygienisches Raumklima.",
      icon: "🏠"
    },
    {
      title: "Bodenreinigung",
      description: "Professionelle Pflege für Hartböden, Fliesen, Laminat und mehr. Wir sorgen für glänzende, hygienisch saubere Oberflächen.",
      icon: "✨"
    },
    {
      title: "Gemeinschaftsräume",
      description: "Zuverlässige Reinigung von Tre<PERSON>nhäusern, Fluren und Gemeinschaftsbereichen für Mehrfamilienhäuser und Wohnanlagen.",
      icon: "🏢"
    },
    {
      title: "Büroreinigung",
      description: "Professionelle Reinigung von Büroflächen und Arbeitsplätzen für ein sauberes und produktives Arbeitsumfeld.",
      icon: "💼"
    },
    {
      title: "Desinfektion",
      description: "Gründliche Desinfektion von Räumen und Oberflächen zur Bekämpfung von Keimen, Bakterien und Viren für maximale Hygiene und Sicherheit.",
      icon: "🧽"
    }
  ];

  return (
    <section
      id="services"
      className="px-4"
      style={{ padding: `var(--section-padding-lg) var(--space-4)` }}
    >
      <div className="max-w-7xl mx-auto">
        <div className="text-center animate-fade-in" style={{ marginBottom: 'var(--space-20)' }}>
          <h2 className="suz-text-display-lg text-slate-800 mb-8">
            Unsere <span className="gradient-text">Leistungen</span>
          </h2>
          <p className="suz-text-heading-lg text-slate-600 max-w-3xl mx-auto">
            Professionelle Reinigungslösungen für jeden Bedarf
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-10">
          {services.map((service, index) => (
            <article
              key={index}
              className="suz-card-glass service-card-premium rounded-3xl border border-white/30 group shadow-2xl animate-fade-in"
              style={{
                animationDelay: `${index * 0.15}s`,
                padding: 'var(--component-padding-lg)'
              }}
            >
              <div className="icon-badge mb-6" role="img" aria-label={`Icon für ${service.title}`}>
                <div className="text-5xl group-hover:scale-110 transition-transform duration-300">
                  {service.icon}
                </div>
              </div>
              <h3 className="suz-text-heading-lg font-semibold text-slate-800 mb-6">
                {service.title}
              </h3>
              <p className="suz-text-body-lg text-slate-600" style={{ lineHeight: 'var(--line-height-relaxed)' }}>
                {service.description}
              </p>
            </article>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Services;
