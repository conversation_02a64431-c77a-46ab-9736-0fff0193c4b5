
import { Mail, Phone, MapPin, Clock, Star } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="py-16 px-4" role="contentinfo" aria-label="Website Footer">
      <div className="max-w-6xl mx-auto">
        {/* Main Footer Content */}
        <div className="suz-card-glass rounded-3xl border border-white/30 shadow-xl mb-8">
          <div className="p-8 md:p-12">
            {/* Footer Header */}
            <div className="text-center mb-12">
              <h2 className="suz-text-heading-xl gradient-text-animated mb-4">
                SUZ Reinigung
              </h2>
              <p className="suz-text-body-lg text-slate-600 max-w-2xl mx-auto">
                Ihr vertrauensvoller Partner für professionelle Reinigungsdienstleistungen
                in höchster Qualität und mit persönlichem Service.
              </p>
            </div>

            {/* Contact Information Grid */}
            <div className="grid md:grid-cols-3 gap-8 mb-12">
              {/* Contact Methods */}
              <div className="text-center">
                <h3 className="suz-text-heading-lg text-slate-700 mb-6 font-semibold">
                  Kontakt
                </h3>
                <div className="space-y-4">
                  <a
                    href="https://wa.me/4917623152477"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="suz-btn-secondary inline-flex items-center gap-3 w-full justify-center suz-focus-ring"
                    aria-label="WhatsApp kontaktieren: +49 176 23152477"
                  >
                    <Phone className="w-5 h-5 text-green-600" />
                    <span>WhatsApp</span>
                  </a>
                  <a
                    href="mailto:<EMAIL>"
                    className="suz-btn-secondary inline-flex items-center gap-3 w-full justify-center suz-focus-ring"
                    aria-label="E-Mail <NAME_EMAIL>"
                  >
                    <Mail className="w-5 h-5 text-blue-600" />
                    <span>E-Mail</span>
                  </a>
                </div>
              </div>

              {/* Service Areas */}
              <div className="text-center">
                <h3 className="suz-text-heading-lg text-slate-700 mb-6 font-semibold">
                  Servicegebiet
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-center gap-2 text-slate-600">
                    <MapPin className="w-4 h-4 text-blue-500" />
                    <span className="suz-text-body-lg">Deutschland</span>
                  </div>
                  <div className="flex items-center justify-center gap-2 text-slate-600">
                    <Clock className="w-4 h-4 text-green-500" />
                    <span className="suz-text-body-lg">24/7 Verfügbar</span>
                  </div>
                  <div className="flex items-center justify-center gap-2 text-slate-600">
                    <Star className="w-4 h-4 text-yellow-500" />
                    <span className="suz-text-body-lg">Premium Service</span>
                  </div>
                </div>
              </div>

              {/* Quick Links */}
              <div className="text-center">
                <h3 className="suz-text-heading-lg text-slate-700 mb-6 font-semibold">
                  Schnellzugriff
                </h3>
                <div className="space-y-3">
                  <button
                    type="button"
                    className="suz-nav-link block w-full text-center suz-focus-ring"
                    onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                    aria-label="Zur Startseite scrollen"
                  >
                    Startseite
                  </button>
                  <button
                    type="button"
                    className="suz-nav-link block w-full text-center suz-focus-ring"
                    onClick={() => document.getElementById('services')?.scrollIntoView({ behavior: 'smooth' })}
                    aria-label="Zu den Leistungen scrollen"
                  >
                    Unsere Leistungen
                  </button>
                  <button
                    type="button"
                    className="suz-nav-link block w-full text-center suz-focus-ring"
                    onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                    aria-label="Zum Kontaktbereich scrollen"
                  >
                    Kontakt aufnehmen
                  </button>
                </div>
              </div>
            </div>

            {/* Website URL */}
            <div className="text-center mb-8">
              <a
                href="https://www.suzreinigung.de"
                target="_blank"
                rel="noopener noreferrer"
                className="suz-text-heading-lg gradient-text font-semibold hover:gradient-text-animated transition-all duration-300 suz-focus-ring"
                aria-label="Website besuchen: www.suzreinigung.de"
              >
                www.suzreinigung.de
              </a>
            </div>
          </div>
        </div>

        {/* Copyright Bar */}
        <div className="suz-card-glass rounded-2xl border border-white/20 px-6 py-4">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="suz-text-body-lg text-slate-600 text-center md:text-left">
              © 2024 SUZ Reinigung. Alle Rechte vorbehalten.
            </p>
            <div className="flex items-center gap-6 text-sm text-slate-500">
              <button
                type="button"
                className="hover:text-slate-700 transition-colors duration-200 suz-focus-ring"
                aria-label="Datenschutz-Informationen"
              >
                Datenschutz
              </button>
              <button
                type="button"
                className="hover:text-slate-700 transition-colors duration-200 suz-focus-ring"
                aria-label="Impressum anzeigen"
              >
                Impressum
              </button>
              <button
                type="button"
                className="hover:text-slate-700 transition-colors duration-200 suz-focus-ring"
                aria-label="Allgemeine Geschäftsbedingungen"
              >
                AGB
              </button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
