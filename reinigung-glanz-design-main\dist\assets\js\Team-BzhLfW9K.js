import{j as e}from"./ui-vendor-WY5xTeZk.js";import"./react-vendor-o6ozJo2K.js";const o=()=>{const t=[{name:"<PERSON>",role:"Teamleitung",image:"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=face"},{name:"<PERSON>",role:"Fachkraft",image:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=300&h=300&fit=crop&crop=face"},{name:"<PERSON><PERSON>",role:"Fachkraft",image:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face"},{name:"<PERSON>",role:"Qualitätskontrolle",image:"https://images.unsplash.com/photo-1531123897727-8f129e1688ce?w=300&h=300&fit=crop&crop=face"}];return e.jsx("section",{id:"team",className:"py-24 px-4 bg-gradient-to-b from-transparent to-blue-50/30",children:e.jsxs("div",{className:"max-w-6xl mx-auto",children:[e.jsxs("div",{className:"text-center mb-20 animate-fade-in",children:[e.jsxs("h2",{className:"text-6xl font-light text-slate-800 mb-8",children:["Unser ",e.jsx("span",{className:"gradient-text",children:"Team"})]}),e.jsx("p",{className:"text-2xl text-slate-600 max-w-3xl mx-auto",children:"Professionelle Reinigungskräfte mit Leidenschaft für Sauberkeit"})]}),e.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:t.map((a,s)=>e.jsxs("div",{className:"team-card glass-morphism-premium p-8 rounded-3xl border border-white/30 text-center hover:scale-105 transition-all duration-500 animate-fade-in shadow-2xl",style:{animationDelay:`${s*.1}s`},children:[e.jsx("div",{className:"mb-6",children:e.jsx("img",{src:a.image,alt:`${a.name} - ${a.role} bei SUZ Reinigung`,className:"w-24 h-24 rounded-full mx-auto object-cover border-4 border-white/50 shadow-lg image-optimized",loading:"lazy",decoding:"async"})}),e.jsx("h3",{className:"text-xl font-semibold text-slate-800 mb-2",children:a.name}),e.jsx("p",{className:"text-blue-600 font-medium",children:a.role})]},s))})]})})};export{o as default};
